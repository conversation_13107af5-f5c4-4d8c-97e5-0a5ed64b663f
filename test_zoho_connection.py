"""
Test Zoho CRM Connection
Simple script to test if Zoho API connection is working
"""

import asyncio
import aiohttp
import json
from app.core.config.settings import settings

async def test_zoho_connection():
    """Test Zoho CRM API connection"""
    
    print("Testing Zoho CRM Connection...")
    print(f"Client ID: {settings.ZOHO_CLIENT_ID}")
    print(f"Base URL: {settings.ZOHO_BASE_URL}")
    print(f"Auth URL: {settings.ZOHO_AUTH_URL}")
    
    try:
        # Step 1: Get access token
        print("\n1. Getting access token...")
        
        data = {
            "refresh_token": settings.ZOHO_REFRESH_TOKEN,
            "client_id": settings.ZOHO_CLIENT_ID,
            "client_secret": settings.ZOHO_CLIENT_SECRET,
            "grant_type": "refresh_token"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(settings.ZOHO_AUTH_URL, data=data) as response:
                if response.status == 200:
                    token_data = await response.json()
                    access_token = token_data["access_token"]
                    print("✅ Access token obtained successfully")
                else:
                    error_text = await response.text()
                    print(f"❌ Failed to get access token: {response.status} - {error_text}")
                    return False
        
        # Step 2: Test API call - Get leads
        print("\n2. Testing API call - Getting leads...")
        
        headers = {
            "Authorization": f"Zoho-oauthtoken {access_token}",
            "Content-Type": "application/json"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{settings.ZOHO_BASE_URL}/Leads?per_page=5", headers=headers) as response:
                if response.status == 200:
                    leads_data = await response.json()
                    leads = leads_data.get("data", [])
                    print(f"✅ Successfully retrieved {len(leads)} leads from Zoho")
                    
                    if leads:
                        print("\nSample lead data:")
                        sample_lead = leads[0]
                        print(f"  - ID: {sample_lead.get('id')}")
                        print(f"  - Name: {sample_lead.get('First_Name', '')} {sample_lead.get('Last_Name', '')}")
                        print(f"  - Email: {sample_lead.get('Email')}")
                        print(f"  - Phone: {sample_lead.get('Phone')}")
                        print(f"  - Created: {sample_lead.get('Created_Time')}")
                    
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Failed to get leads: {response.status} - {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_zoho_connection())
    if result:
        print("\n🎉 Zoho CRM connection is working perfectly!")
    else:
        print("\n💥 Zoho CRM connection failed. Please check your credentials.")
