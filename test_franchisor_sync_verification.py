#!/usr/bin/env python3
"""
Test to verify franchisors are being pulled from Zoho and saved to database
"""

import asyncio
import httpx
import uuid

BASE_URL = "http://localhost:8000"

async def test_franchisor_sync():
    async with httpx.AsyncClient() as client:
        # Register and login
        user_email = f"sync_test_{str(uuid.uuid4())[:8]}@example.com"
        register_data = {
            "email": user_email,
            "password": "Test123!",
            "confirm_password": "Test123!",
            "first_name": "Sync",
            "last_name": "Test",
            "mobile": f"+{str(uuid.uuid4().int)[:10]}"
        }
        
        response = await client.post(f"{BASE_URL}/api/auth/register", json=register_data)
        if response.status_code != 201:
            print(f"❌ Registration failed: {response.status_code}")
            return
        
        login_data = {
            "email_or_mobile": user_email,
            "password": "Test123!"
        }
        
        response = await client.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if response.status_code != 200:
            print(f"❌ Login failed: {response.status_code}")
            return
        
        data = response.json()
        access_token = data["data"]["details"]["access_token"]
        headers = {"Authorization": f"Bearer {access_token}"}
        
        print("✅ Login successful")
        
        # 1. Get current franchisor count
        print("\n1. Getting current franchisor count...")
        response = await client.get(f"{BASE_URL}/api/franchisors/", headers=headers)
        if response.status_code == 200:
            data = response.json()
            before_count = data.get("data", {}).get("total_count", 0)
            print(f"   📊 Current franchisors in DB: {before_count}")
        else:
            print(f"   ❌ Failed to get franchisors: {response.status_code}")
            return
        
        # 2. Trigger Zoho sync
        print("\n2. Triggering Zoho sync...")
        response = await client.post(f"{BASE_URL}/api/zoho/sync", headers=headers)
        if response.status_code == 200:
            sync_data = response.json()
            print("   ✅ Zoho sync completed")
            
            # Show sync results
            sync_results = sync_data.get("data", {})
            franchisors_pulled = sync_results.get("franchisors_pulled", 0)
            franchisors_updated = sync_results.get("franchisors_updated", 0)
            leads_pulled = sync_results.get("leads_pulled", 0)
            leads_pushed = sync_results.get("leads_pushed", 0)
            
            print(f"   📥 Franchisors pulled: {franchisors_pulled}")
            print(f"   🔄 Franchisors updated: {franchisors_updated}")
            print(f"   📥 Leads pulled: {leads_pulled}")
            print(f"   📤 Leads pushed: {leads_pushed}")
        else:
            print(f"   ❌ Zoho sync failed: {response.status_code}")
            if response.status_code != 500:
                error_data = response.json()
                print(f"   Error: {error_data.get('message', 'Unknown error')}")
            return
        
        # 3. Get franchisor count after sync
        print("\n3. Getting franchisor count after sync...")
        response = await client.get(f"{BASE_URL}/api/franchisors/", headers=headers)
        if response.status_code == 200:
            data = response.json()
            after_count = data.get("data", {}).get("total_count", 0)
            print(f"   📊 Franchisors in DB after sync: {after_count}")
            
            # Calculate difference
            difference = after_count - before_count
            if difference > 0:
                print(f"   ✅ {difference} new franchisors added to database!")
            elif difference == 0:
                print(f"   ℹ️ No new franchisors added (may have been updated instead)")
            else:
                print(f"   ⚠️ Unexpected: {abs(difference)} fewer franchisors")
        else:
            print(f"   ❌ Failed to get franchisors after sync: {response.status_code}")
            return
        
        # 4. Check for franchisors with Zoho IDs
        print("\n4. Checking for franchisors with Zoho IDs...")
        response = await client.get(f"{BASE_URL}/api/franchisors/?limit=50", headers=headers)
        if response.status_code == 200:
            data = response.json()
            items = data.get("data", {}).get("items", [])
            
            zoho_franchisors = []
            sale_won_franchisors = []
            
            for item in items:
                # Check if it has franchisor_won_id (from Zoho)
                if item.get("franchisor_won_id"):
                    sale_won_franchisors.append(item)
                    zoho_franchisors.append(item)
                # Or check if it has any Zoho-related data
                elif any(field in str(item).lower() for field in ['zoho', 'sale won']):
                    zoho_franchisors.append(item)
            
            print(f"   📋 Total franchisors checked: {len(items)}")
            print(f"   🎯 Franchisors with franchisor_won_id: {len(sale_won_franchisors)}")
            
            if sale_won_franchisors:
                print("   📝 Sample franchisors with franchisor_won_id:")
                for i, franchisor in enumerate(sale_won_franchisors[:3], 1):
                    won_id = franchisor.get("franchisor_won_id", "N/A")
                    name = franchisor.get("name", "N/A")
                    industry = franchisor.get("industry_details", {}).get("name", "N/A")
                    print(f"      {i}. {name} (Won ID: {won_id}, Industry: {industry})")
            else:
                print("   ⚠️ No franchisors found with franchisor_won_id")
                print("   💡 This might mean:")
                print("      - No franchisors in Zoho have 'Sale Won - 100%' status")
                print("      - Zoho field mapping needs to be checked")
                print("      - Zoho API credentials might be invalid")
        
        # 5. Test DESC sorting with NULL values
        print("\n5. Testing DESC sorting on industry_name (with NULL handling)...")
        response = await client.get(
            f"{BASE_URL}/api/franchisors/",
            params={"sort_by": "industry_name", "sort_order": "desc", "limit": 5},
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            items = data.get("data", {}).get("items", [])
            print(f"   ✅ DESC sorting works: {len(items)} franchisors returned")
            
            print("   📋 Sorted results (DESC with NULL handling):")
            for i, item in enumerate(items, 1):
                industry_name = item.get("industry_details", {}).get("name", "NULL")
                franchisor_name = item.get("name", "N/A")
                print(f"      {i}. {franchisor_name} - Industry: {industry_name}")
        else:
            print(f"   ❌ DESC sorting failed: {response.status_code}")
        
        print("\n✅ Franchisor sync verification completed!")

if __name__ == "__main__":
    asyncio.run(test_franchisor_sync())
