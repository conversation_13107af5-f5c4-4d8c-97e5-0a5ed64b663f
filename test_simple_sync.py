"""
Test the simple Zoho sync functionality
"""

import asyncio
from app.services.simple_zoho_sync import SimpleZohoSync
from app.core.database.connection import get_db

async def test_sync():
    """Test the sync functionality"""

    print("Testing Simple Zoho Sync...")

    # Get database session
    async for db in get_db():
        sync_service = SimpleZohoSync(db)
        
        # Test sync status first
        print("\n1. Getting sync status...")
        status_result = await sync_service.get_sync_status()
        
        if status_result["success"]:
            data = status_result["data"]
            print(f"✅ Sync status retrieved:")
            print(f"   - Synced leads: {data['synced_leads']}")
            print(f"   - Unsynced leads: {data['unsynced_leads']}")
            print(f"   - Total leads: {data['total_leads']}")
        else:
            print(f"❌ Failed to get sync status: {status_result['message']}")
            return
        
        # Test actual sync
        print("\n2. Running sync with Zoho...")
        sync_result = await sync_service.sync_with_zoho()
        
        if sync_result["success"]:
            data = sync_result["data"]
            print(f"✅ Sync completed successfully:")
            print(f"   - Leads pulled: {data['leads_pulled']}")
            print(f"   - Leads pushed: {data['leads_pushed']}")
            print(f"   - Leads updated: {data['leads_updated']}")
            print(f"   - Conflicts resolved: {data['conflicts_resolved']}")
            if data['errors']:
                print(f"   - Errors: {data['errors']}")
        else:
            print(f"❌ Sync failed: {sync_result['message']}")
            return
        
        # Check status again
        print("\n3. Getting sync status after sync...")
        status_result = await sync_service.get_sync_status()
        
        if status_result["success"]:
            data = status_result["data"]
            print(f"✅ Updated sync status:")
            print(f"   - Synced leads: {data['synced_leads']}")
            print(f"   - Unsynced leads: {data['unsynced_leads']}")
            print(f"   - Total leads: {data['total_leads']}")
        
        print("\n🎉 Test completed successfully!")
        break  # Only use first db session

if __name__ == "__main__":
    asyncio.run(test_sync())
