#!/usr/bin/env python3
"""
Test lead creation directly
"""

import asyncio
import uuid
from app.core.database.connection import get_db
from app.models.lead import Lead

async def test_lead_creation():
    """Test creating a lead directly"""

    print("🧪 Testing Lead Creation")
    print("=" * 40)

    try:
        async for db in get_db():
            # Default lead status ID for "New Lead"
            default_lead_status_id = uuid.UUID("f53c50cf-9374-4d18-98c1-c905215051eb")
            
            # Create test lead
            lead = Lead(
                first_name="Test",
                last_name="User",
                phone="1111111111",
                lead_status_id=default_lead_status_id
            )
            
            print(f"📝 Creating lead: {lead.first_name} {lead.last_name}")
            print(f"📞 Phone: {lead.phone}")
            print(f"🆔 Lead Status ID: {lead.lead_status_id}")
            
            db.add(lead)
            await db.commit()
            await db.refresh(lead)
            
            print(f"✅ Lead created successfully!")
            print(f"🆔 Lead ID: {lead.id}")
            print(f"📅 Created at: {lead.created_at}")
            
    except Exception as e:
        print(f"❌ Error creating lead: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_lead_creation())
