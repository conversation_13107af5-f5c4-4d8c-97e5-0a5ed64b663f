#!/bin/bash
"""
Start Celery Worker Locally
For development without Dock<PERSON>
"""

set -e

echo "🚀 Starting Celery Worker for GrowthHive..."

# Check if virtual environment is activated
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "⚠️  Virtual environment not detected. Activating venv311..."
    source venv311/bin/activate
fi

# Check if RabbitMQ is running (assuming local installation)
echo "🔍 Checking RabbitMQ connection..."
if ! python -c "import pika; pika.BlockingConnection(pika.URLParameters('amqp://growthhive:growthhive123@localhost:5672//'))" 2>/dev/null; then
    echo "❌ Cannot connect to RabbitMQ. Please ensure RabbitMQ is running with correct credentials."
    echo "   You can start it with Docker: docker-compose -f docker-compose.rabbitmq.yml up -d rabbitmq"
    exit 1
fi
echo "✅ RabbitMQ connection successful"

# Check if Redis is running
echo "🔍 Checking Redis connection..."
if ! python -c "import redis; redis.Redis(host='localhost', port=6379, db=0).ping()" 2>/dev/null; then
    echo "❌ Cannot connect to <PERSON><PERSON>. Please ensure <PERSON><PERSON> is running."
    echo "   You can start it with Docker: docker-compose -f docker-compose.rabbitmq.yml up -d redis"
    exit 1
fi
echo "✅ Redis connection successful"

# Start Celery worker
echo "👷 Starting Celery worker..."
echo "   Queue: document_processing"
echo "   Concurrency: 4"
echo "   Log level: info"
echo ""

# Run Celery worker
python celery_worker.py --loglevel=info --concurrency=4 --queues=document_processing

echo "🛑 Celery worker stopped"
