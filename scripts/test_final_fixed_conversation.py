#!/usr/bin/env python3
"""
Test final fixed conversation flow
"""

import requests
import json
from datetime import datetime

def test_final_fixed_conversation():
    """Test conversation with all database fixes applied"""
    
    webhook_url = "http://localhost:8000/api/webhooks/webhooks/kudosity"
    
    # Use a completely new phone number
    test_phone = "4444444444"
    
    # Test messages for conversation flow
    test_messages = [
        "Hello",
        "<PERSON> Wilson"
    ]
    
    print(f"🤖 Testing Final Fixed Conversation - Phone: {test_phone}")
    print("=" * 60)
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n📱 Message {i}: {message}")
        
        # Create webhook payload
        payload = {
            "event_type": "SMS_INBOUND",
            "timestamp": datetime.now().isoformat() + "Z",
            "mo": {
                "type": "SMS",
                "id": f"final_fixed_test_{i:03d}",
                "message": message,
                "recipient": "1234567890",
                "sender": test_phone,
                "routed_via": "1234567890"
            }
        }
        
        try:
            # Send webhook request
            response = requests.post(
                webhook_url,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                response_data = response.json()
                
                # Check if AI response is in the response
                if 'data' in response_data and 'ai_response' in response_data['data']:
                    ai_response = response_data['data']['ai_response']
                    ai_success = response_data['data'].get('ai_success', False)
                    ai_metadata = response_data['data'].get('ai_metadata', {})
                    
                    print(f"🤖 AI Response: {ai_response}")
                    print(f"✅ Success: {ai_success}")
                    if ai_metadata:
                        stage = ai_metadata.get('conversation_stage', 'unknown')
                        session_id = ai_metadata.get('session_id', 'unknown')
                        print(f"📊 Stage: {stage}")
                        print(f"🆔 Session: {session_id}")
                else:
                    print("❌ No AI response in webhook response")
                    print(f"📄 Response: {json.dumps(response_data, indent=2)}")
                    
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"📄 Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
            
        print("-" * 40)
        
        # Small delay between messages
        import time
        time.sleep(1)
    
    print("\n✅ Final fixed conversation test completed!")

if __name__ == "__main__":
    test_final_fixed_conversation()
