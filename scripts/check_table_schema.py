#!/usr/bin/env python3
"""
Check database table schema
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from app.core.database.connection import get_db
from contextlib import asynccontextmanager


@asynccontextmanager
async def get_db_session():
    """Helper to get database session as context manager"""
    async for db in get_db():
        yield db
        break


async def check_table_schema():
    """Check the schema of conversation-related tables"""
    async with get_db_session() as db:
        # Check if pre_qualification_questions table exists and its columns
        result = await db.execute(text("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'pre_qualification_questions'
            ORDER BY ordinal_position;
        """))
        
        print("=== pre_qualification_questions table schema ===")
        columns = result.fetchall()
        if columns:
            for col in columns:
                print(f"  {col[0]}: {col[1]} (nullable: {col[2]}, default: {col[3]})")
        else:
            print("  Table does not exist")
        
        # Check if sales_script table exists and its columns
        result = await db.execute(text("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'sales_script'
            ORDER BY ordinal_position;
        """))
        
        print("\n=== sales_script table schema ===")
        columns = result.fetchall()
        if columns:
            for col in columns:
                print(f"  {col[0]}: {col[1]} (nullable: {col[2]}, default: {col[3]})")
        else:
            print("  Table does not exist")
        
        # Check if conversation_session table exists and its columns
        result = await db.execute(text("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'conversation_session'
            ORDER BY ordinal_position;
        """))
        
        print("\n=== conversation_session table schema ===")
        columns = result.fetchall()
        if columns:
            for col in columns:
                print(f"  {col[0]}: {col[1]} (nullable: {col[2]}, default: {col[3]})")
        else:
            print("  Table does not exist")


if __name__ == "__main__":
    asyncio.run(check_table_schema())
