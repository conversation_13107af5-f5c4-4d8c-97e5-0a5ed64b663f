-- Quick fix for pre_qualification_questions table
-- Add missing columns

ALTER TABLE pre_qualification_questions 
ADD COLUMN IF NOT EXISTS question_type VARCHAR(50) DEFAULT 'text';

ALTER TABLE pre_qualification_questions 
ADD COLUMN IF NOT EXISTS order_sequence INTEGER DEFAULT 1;

ALTER TABLE pre_qualification_questions 
ADD COLUMN IF NOT EXISTS qualification_weight FLOAT DEFAULT 1.0;

ALTER TABLE pre_qualification_questions 
ADD COLUMN IF NOT EXISTS expected_answer_type VARCHAR(50) DEFAULT 'text';

ALTER TABLE pre_qualification_questions 
ADD COLUMN IF NOT EXISTS validation_rules TEXT;

ALTER TABLE pre_qualification_questions 
ADD COLUMN IF NOT EXISTS answer_options TEXT;

ALTER TABLE pre_qualification_questions 
ADD COLUMN IF NOT EXISTS requires_follow_up BOOLEAN DEFAULT FALSE;

ALTER TABLE pre_qualification_questions 
ADD COLUMN IF NOT EXISTS follow_up_logic TEXT;

ALTER TABLE pre_qualification_questions
ADD COLUMN IF NOT EXISTS passing_criteria TEXT;

ALTER TABLE pre_qualification_questions
ADD COLUMN IF NOT EXISTS is_required BOOLEAN DEFAULT TRUE;

-- Insert sample data
INSERT INTO sales_script (script_title, script_content, script_stage, order_sequence) VALUES
('Initial greeting', 'Hi! I''m here to help you explore the franchise opportunity. Let''s get started!', 'initial_greeting', 1),
('Qualification Introduction', 'I''d like to ask you a few questions to better understand your interests and see if this franchise opportunity is a good fit for you.', 'prequalification', 1),
('Document Q&A Introduction', 'Great! Now I can answer questions about our franchise opportunity based on our brochure and documentation. What would you like to know?', 'document_qa', 1),
('Goodbye', 'Thank you for your interest in our franchise opportunity! If you have any more questions in the future, feel free to reach out. Have a great day!', 'goodbye', 1)
ON CONFLICT (script_title) DO NOTHING;

-- Clear existing questions first
DELETE FROM pre_qualification_questions;

INSERT INTO pre_qualification_questions (id, question_id, category, question_text, expected_answers, score_weight, is_active, order_sequence, qualification_weight, expected_answer_type, is_required, passing_criteria, answer_options, question_type) VALUES
(gen_random_uuid(), 'Q001', 'personal', 'What''s your name?', '[]', 10, true, 1, 0.1, 'text', true, '{"min_length": 2}', null, 'text'),
(gen_random_uuid(), 'Q002', 'financial', 'What''s your budget range for this franchise investment? (A) Under $50k (B) $50k-$100k (C) $100k-$200k (D) Over $200k', '["B", "C", "D"]', 40, true, 2, 0.4, 'choice', true, '{"qualifying_answers": ["B", "C", "D"]}', '["A", "B", "C", "D"]', 'multiple_choice'),
(gen_random_uuid(), 'Q003', 'experience', 'Do you have previous business or management experience? (Yes/No)', '["Yes"]', 30, true, 3, 0.3, 'boolean', true, '{"qualifying_answers": ["Yes"]}', '["Yes", "No"]', 'yes_no'),
(gen_random_uuid(), 'Q004', 'involvement', 'Are you looking to be an owner-operator or an investor? (A) Owner-operator (B) Investor (C) Both', '["A", "C"]', 20, true, 4, 0.2, 'choice', true, '{"qualifying_answers": ["A", "C"]}', '["A", "B", "C"]', 'multiple_choice');
