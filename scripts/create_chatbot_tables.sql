-- Create conversational chatbot tables manually for testing

-- Create sales_script table
CREATE TABLE IF NOT EXISTS sales_script (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    script_title VARCHAR(100) NOT NULL UNIQUE,
    script_content TEXT NOT NULL,
    script_stage VARCHAR(50) NOT NULL,
    order_sequence INTEGER NOT NULL DEFAULT 1,
    has_variables BOOLEAN DEFAULT FALSE NOT NULL,
    variable_schema TEXT,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    is_deleted BOOLEAN DEFAULT FALSE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for sales_script
CREATE INDEX IF NOT EXISTS ix_sales_script_script_title ON sales_script(script_title);
CREATE INDEX IF NOT EXISTS ix_sales_script_script_stage ON sales_script(script_stage);
CREATE INDEX IF NOT EXISTS ix_sales_script_is_active ON sales_script(is_active);
CREATE INDEX IF NOT EXISTS ix_sales_script_is_deleted ON sales_script(is_deleted);

-- Create pre_qualification_questions table
CREATE TABLE IF NOT EXISTS pre_qualification_questions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    question_text TEXT NOT NULL,
    question_type VARCHAR(50) NOT NULL,
    order_sequence INTEGER NOT NULL DEFAULT 1,
    qualification_weight FLOAT NOT NULL DEFAULT 1.0,
    passing_criteria TEXT,
    expected_answer_type VARCHAR(50) NOT NULL DEFAULT 'text',
    validation_rules TEXT,
    answer_options TEXT,
    requires_follow_up BOOLEAN DEFAULT FALSE NOT NULL,
    follow_up_logic TEXT,
    is_required BOOLEAN DEFAULT TRUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    is_deleted BOOLEAN DEFAULT FALSE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for pre_qualification_questions
CREATE INDEX IF NOT EXISTS ix_pre_qualification_questions_question_type ON pre_qualification_questions(question_type);
CREATE INDEX IF NOT EXISTS ix_pre_qualification_questions_is_active ON pre_qualification_questions(is_active);
CREATE INDEX IF NOT EXISTS ix_pre_qualification_questions_is_deleted ON pre_qualification_questions(is_deleted);

-- Create conversation_sessions table
CREATE TABLE IF NOT EXISTS conversation_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    phone_number VARCHAR(20) NOT NULL,
    session_id VARCHAR(100) NOT NULL UNIQUE,
    lead_id UUID REFERENCES leads(id),
    current_stage VARCHAR(50) NOT NULL DEFAULT 'initial_greeting',
    current_question_id UUID REFERENCES pre_qualification_questions(id),
    questions_asked INTEGER NOT NULL DEFAULT 0,
    questions_answered INTEGER NOT NULL DEFAULT 0,
    qualification_score VARCHAR(10),
    is_qualified BOOLEAN,
    conversation_context TEXT,
    last_message_sent TEXT,
    last_message_received TEXT,
    message_count INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    is_completed BOOLEAN DEFAULT FALSE NOT NULL,
    completion_reason VARCHAR(100),
    started_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create indexes for conversation_sessions
CREATE INDEX IF NOT EXISTS ix_conversation_sessions_phone_number ON conversation_sessions(phone_number);
CREATE INDEX IF NOT EXISTS ix_conversation_sessions_session_id ON conversation_sessions(session_id);
CREATE INDEX IF NOT EXISTS ix_conversation_sessions_lead_id ON conversation_sessions(lead_id);
CREATE INDEX IF NOT EXISTS ix_conversation_sessions_current_stage ON conversation_sessions(current_stage);
CREATE INDEX IF NOT EXISTS ix_conversation_sessions_is_active ON conversation_sessions(is_active);
