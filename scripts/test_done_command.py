#!/usr/bin/env python3
"""
Test done command specifically
"""

import requests
import json
from datetime import datetime

def test_done_command():
    """Test done command to end conversation"""
    
    webhook_url = "http://localhost:8000/api/webhooks/webhooks/kudosity"
    
    # Use the existing phone number that's in document_qa stage
    test_phone = "1010101010"
    
    # Test message - should end conversation
    message = "done"
    
    print(f"🤖 Testing Done Command - Phone: {test_phone}")
    print("=" * 60)
    print(f"\n📱 Message: {message}")
    print("📊 Expected: Should move to goodbye stage")
    
    # Create webhook payload
    payload = {
        "event_type": "SMS_INBOUND",
        "timestamp": datetime.now().isoformat() + "Z",
        "mo": {
            "type": "SMS",
            "id": "done_command_test_001",
            "message": message,
            "recipient": "1234567890",
            "sender": test_phone,
            "routed_via": "1234567890"
        }
    }
    
    try:
        # Send webhook request
        response = requests.post(
            webhook_url,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            response_data = response.json()
            
            # Check if AI response is in the response
            if 'data' in response_data and 'ai_response' in response_data['data']:
                ai_response = response_data['data']['ai_response']
                ai_success = response_data['data'].get('ai_success', False)
                ai_metadata = response_data['data'].get('ai_metadata', {})
                
                actual_stage = ai_metadata.get('conversation_stage', 'unknown')
                session_id = ai_metadata.get('session_id', 'unknown')
                
                print(f"🤖 AI Response: {ai_response}")
                print(f"✅ Success: {ai_success}")
                print(f"📊 Stage: {actual_stage}")
                print(f"🆔 Session: {session_id}")
                
                if actual_stage == "goodbye":
                    print("✅ Done command worked! Moved to goodbye stage.")
                else:
                    print(f"❌ Done command failed. Expected goodbye, got {actual_stage}")
                    
            else:
                print("❌ No AI response in webhook response")
                print(f"📄 Response: {json.dumps(response_data, indent=2)}")
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"📄 Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n✅ Done command test completed!")

if __name__ == "__main__":
    test_done_command()
