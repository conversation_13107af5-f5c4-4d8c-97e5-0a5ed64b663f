#!/usr/bin/env python3
"""
Quick fix for missing columns and seed conversation data
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, select
from app.core.database.connection import get_db
from contextlib import asynccontextmanager
from app.models.sales_script import SalesScript
from app.models.pre_qualification_question import PreQualificationQuestion


@asynccontextmanager
async def get_db_session():
    """Helper to get database session as context manager"""
    async for db in get_db():
        yield db
        break


async def fix_schema_and_seed():
    """Fix schema issues and seed data"""
    async with get_db_session() as db:
        print("🔧 Fixing database schema...")
        
        # Add missing columns if they don't exist
        try:
            await db.execute(text("""
                ALTER TABLE pre_qualification_questions
                ADD COLUMN IF NOT EXISTS question_type VARCHAR(50) DEFAULT 'text';
            """))
            await db.execute(text("""
                ALTER TABLE pre_qualification_questions
                ADD COLUMN IF NOT EXISTS order_sequence INTEGER DEFAULT 1;
            """))
            await db.execute(text("""
                ALTER TABLE pre_qualification_questions
                ADD COLUMN IF NOT EXISTS qualification_weight FLOAT DEFAULT 1.0;
            """))
            await db.execute(text("""
                ALTER TABLE pre_qualification_questions
                ADD COLUMN IF NOT EXISTS expected_answer_type VARCHAR(50) DEFAULT 'text';
            """))
            await db.execute(text("""
                ALTER TABLE pre_qualification_questions
                ADD COLUMN IF NOT EXISTS validation_rules TEXT;
            """))
            await db.execute(text("""
                ALTER TABLE pre_qualification_questions
                ADD COLUMN IF NOT EXISTS answer_options TEXT;
            """))
            await db.execute(text("""
                ALTER TABLE pre_qualification_questions
                ADD COLUMN IF NOT EXISTS requires_follow_up BOOLEAN DEFAULT FALSE;
            """))
            await db.execute(text("""
                ALTER TABLE pre_qualification_questions
                ADD COLUMN IF NOT EXISTS follow_up_logic TEXT;
            """))
            await db.execute(text("""
                ALTER TABLE pre_qualification_questions
                ADD COLUMN IF NOT EXISTS passing_criteria TEXT;
            """))
            await db.commit()
            print("✅ Schema fixed")
        except Exception as e:
            print(f"⚠️ Schema fix error (might be OK): {e}")
        
        # Seed sales scripts
        print("🌱 Seeding sales scripts...")
        scripts = [
            {
                "script_title": "Initial greeting",
                "script_content": "Hi! I'm here to help you explore the franchise opportunity. Let's get started!",
                "script_stage": "initial_greeting",
                "order_sequence": 1
            },
            {
                "script_title": "Qualification Introduction",
                "script_content": "I'd like to ask you a few questions to better understand your interests and see if this franchise opportunity is a good fit for you.",
                "script_stage": "prequalification",
                "order_sequence": 1
            },
            {
                "script_title": "Document Q&A Introduction",
                "script_content": "Great! Now I can answer questions about our franchise opportunity based on our brochure and documentation. What would you like to know?",
                "script_stage": "document_qa",
                "order_sequence": 1
            },
            {
                "script_title": "Goodbye",
                "script_content": "Thank you for your interest in our franchise opportunity! If you have any more questions in the future, feel free to reach out. Have a great day!",
                "script_stage": "goodbye",
                "order_sequence": 1
            }
        ]
        
        for script_data in scripts:
            stmt = select(SalesScript.id).where(SalesScript.script_title == script_data['script_title'])
            existing = await db.execute(stmt)
            if not existing.scalar_one_or_none():
                script = SalesScript(**script_data)
                db.add(script)
        
        await db.commit()
        print("✅ Sales scripts seeded")
        
        # Seed pre-qualification questions
        print("🌱 Seeding pre-qualification questions...")
        questions = [
            {
                "question_text": "What's your name?",
                "question_type": "text",
                "order_sequence": 1,
                "qualification_weight": 0.1,
                "expected_answer_type": "text",
                "is_required": True,
                "passing_criteria": '{"min_length": 2}'
            },
            {
                "question_text": "What's your budget range for this franchise investment? (A) Under $50k (B) $50k-$100k (C) $100k-$200k (D) Over $200k",
                "question_type": "multiple_choice",
                "order_sequence": 2,
                "qualification_weight": 0.4,
                "expected_answer_type": "choice",
                "is_required": True,
                "answer_options": '["A", "B", "C", "D"]',
                "passing_criteria": '{"qualifying_answers": ["B", "C", "D"]}'
            },
            {
                "question_text": "Do you have previous business or management experience? (Yes/No)",
                "question_type": "yes_no",
                "order_sequence": 3,
                "qualification_weight": 0.3,
                "expected_answer_type": "boolean",
                "is_required": True,
                "answer_options": '["Yes", "No"]',
                "passing_criteria": '{"qualifying_answers": ["Yes"]}'
            },
            {
                "question_text": "Are you looking to be an owner-operator or an investor? (A) Owner-operator (B) Investor (C) Both",
                "question_type": "multiple_choice",
                "order_sequence": 4,
                "qualification_weight": 0.2,
                "expected_answer_type": "choice",
                "is_required": True,
                "answer_options": '["A", "B", "C"]',
                "passing_criteria": '{"qualifying_answers": ["A", "C"]}'
            }
        ]
        
        for question_data in questions:
            stmt = select(PreQualificationQuestion.id).where(PreQualificationQuestion.question_text == question_data['question_text'])
            existing = await db.execute(stmt)
            if not existing.scalar_one_or_none():
                question = PreQualificationQuestion(**question_data)
                db.add(question)
        
        await db.commit()
        print("✅ Pre-qualification questions seeded")
        print("🎉 All data seeded successfully!")


if __name__ == "__main__":
    asyncio.run(fix_schema_and_seed())
