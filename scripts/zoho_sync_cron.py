#!/usr/bin/env python3
"""
Zoho CRM Sync Cron Job Script
This script is designed to be run by cron every 15 minutes
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/var/log/zoho_sync_cron.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

async def main():
    """Main cron job execution"""
    try:
        # Import after setting up the path
        from app.services.zoho_sync_cron import run_zoho_sync_job
        
        logger.info("Starting Zoho CRM sync cron job...")
        results = await run_zoho_sync_job()
        
        if results["status"] == "success":
            logger.info("Cron job completed successfully")
            sys.exit(0)
        elif results["status"] == "completed_with_errors":
            logger.warning("Cron job completed with errors")
            sys.exit(1)
        else:
            logger.error("Cron job failed")
            sys.exit(2)
            
    except Exception as e:
        logger.error(f"Cron job execution failed: {e}", exc_info=True)
        sys.exit(3)

if __name__ == "__main__":
    asyncio.run(main())
