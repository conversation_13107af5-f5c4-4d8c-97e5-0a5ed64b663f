#!/usr/bin/env python3
"""
Complete SMS Chat Flow Test
Tests the entire conversation flow from greeting to goodbye
"""

import requests
import json
from datetime import datetime
import time

def test_complete_sms_flow():
    """Test the complete SMS conversation flow"""
    
    webhook_url = "http://localhost:8000/api/webhooks/webhooks/kudosity"
    
    # Use a completely new phone number for fresh conversation
    test_phone = "6666666666"
    
    # Complete conversation flow test messages
    test_messages = [
        # Stage 1: Initial Greeting
        {
            "message": "Hello!",
            "expected_stage": "prequalification",
            "description": "Initial greeting - should move to prequalification"
        },
        
        # Stage 2: Prequalification - Name
        {
            "message": "<PERSON>",
            "expected_stage": "prequalification", 
            "description": "Provide name - should ask next question"
        },
        
        # Stage 3: Prequalification - Budget
        {
            "message": "C",
            "expected_stage": "prequalification",
            "description": "Answer budget question - should ask next question"
        },
        
        # Stage 4: Prequalification - Owner/Investor
        {
            "message": "A", 
            "expected_stage": "document_qa",
            "description": "Answer owner/investor question - should move to document Q&A"
        },
        
        # Stage 5: Document Q&A - Ask about franchise
        {
            "message": "What is the initial investment required?",
            "expected_stage": "document_qa",
            "description": "Ask about investment - should get RAG answer"
        },
        
        # Stage 6: Document Q&A - Ask another question
        {
            "message": "What training is provided?",
            "expected_stage": "document_qa", 
            "description": "Ask about training - should get RAG answer"
        },
        
        # Stage 7: End conversation
        {
            "message": "done",
            "expected_stage": "goodbye",
            "description": "End conversation - should move to goodbye"
        }
    ]
    
    print(f"🤖 Testing Complete SMS Flow - Phone: {test_phone}")
    print("=" * 80)
    
    for i, test_case in enumerate(test_messages, 1):
        message = test_case["message"]
        expected_stage = test_case["expected_stage"]
        description = test_case["description"]
        
        print(f"\n📱 Step {i}: {description}")
        print(f"📝 Message: '{message}'")
        print(f"🎯 Expected Stage: {expected_stage}")
        
        # Create webhook payload
        payload = {
            "event_type": "SMS_INBOUND",
            "timestamp": datetime.now().isoformat() + "Z",
            "mo": {
                "type": "SMS",
                "id": f"complete_flow_test_{i:03d}",
                "message": message,
                "recipient": "1234567890",
                "sender": test_phone,
                "routed_via": "1234567890"
            }
        }
        
        try:
            # Send webhook request
            response = requests.post(
                webhook_url,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                response_data = response.json()
                
                # Check if AI response is in the response
                if 'data' in response_data and 'ai_response' in response_data['data']:
                    ai_response = response_data['data']['ai_response']
                    ai_success = response_data['data'].get('ai_success', False)
                    ai_metadata = response_data['data'].get('ai_metadata', {})
                    
                    actual_stage = ai_metadata.get('conversation_stage', 'unknown')
                    session_id = ai_metadata.get('session_id', 'unknown')
                    
                    print(f"🤖 AI Response: {ai_response[:200]}{'...' if len(ai_response) > 200 else ''}")
                    print(f"✅ Success: {ai_success}")
                    print(f"📊 Actual Stage: {actual_stage}")
                    print(f"🆔 Session: {session_id}")
                    
                    # Check if stage matches expected
                    if actual_stage == expected_stage:
                        print(f"✅ Stage Correct: {actual_stage}")
                    else:
                        print(f"❌ Stage Mismatch: Expected {expected_stage}, Got {actual_stage}")
                        
                else:
                    print("❌ No AI response in webhook response")
                    print(f"📄 Response: {json.dumps(response_data, indent=2)}")
                    
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"📄 Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
            
        print("-" * 60)
        
        # Small delay between messages to simulate real conversation
        time.sleep(2)
    
    print("\n🎉 Complete SMS flow test completed!")
    print("\n📊 Summary:")
    print("- Initial Greeting → Prequalification")
    print("- Name Collection → Next Question") 
    print("- Budget Question → Next Question")
    print("- Owner/Investor → Document Q&A")
    print("- Investment Question → RAG Answer")
    print("- Training Question → RAG Answer")
    print("- Done → Goodbye")

if __name__ == "__main__":
    test_complete_sms_flow()
