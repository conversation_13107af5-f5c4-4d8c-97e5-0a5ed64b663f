#!/usr/bin/env python3
"""
Simple webhook test
"""

import requests
import json
from datetime import datetime

def test_simple_webhook():
    """Test simple webhook call"""
    
    webhook_url = "http://localhost:8000/api/webhooks/webhooks/kudosity"
    
    # Simple test payload
    payload = {
        "event_type": "SMS_INBOUND",
        "timestamp": datetime.now().isoformat() + "Z",
        "mo": {
            "type": "SMS",
            "id": "simple_test_001",
            "message": "Hello",
            "recipient": "1234567890",
            "sender": "9999999999",
            "routed_via": "1234567890"
        }
    }
    
    print("🤖 Testing Simple Webhook")
    print("=" * 40)
    
    try:
        # Send webhook request
        response = requests.post(
            webhook_url,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"📋 JSON Response: {json.dumps(response_data, indent=2)}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_simple_webhook()
