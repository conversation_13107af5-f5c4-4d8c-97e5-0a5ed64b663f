#!/usr/bin/env python3
"""
Direct test of conversation agent
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.agents.conversation_agent import ConversationAgent
from app.agents.base import AgentConfig, AgentRole


async def test_conversation_agent_direct():
    """Test conversation agent directly"""
    print("🤖 Testing Conversation Agent Directly...")
    
    # Create agent config
    config = AgentConfig(
        role=AgentRole.CONVERSATIONAL_CHATBOT,
        name="Test Conversation Agent",
        description="Test conversational chatbot agent"
    )
    
    # Initialize agent
    agent = ConversationAgent(config)
    
    # Test phone number
    test_phone = "+1234567890"
    
    print(f"📱 Testing with phone: {test_phone}")
    print(f"👤 User message: Hello")
    
    # Create state
    state = {
        "user_input": "Hello",
        "context": {"sender": test_phone},
        "messages": []
    }
    
    try:
        # Process message
        print("🔄 Processing message...")
        result = await agent.process_state(state)
        
        print(f"✅ Result: {result}")
        
        # Check if response exists
        if "response" in result:
            print(f"🤖 AI Response: {result['response']}")
        else:
            print("❌ No response in result")
            
        if "error" in result:
            print(f"❌ Error: {result['error']}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_conversation_agent_direct())
