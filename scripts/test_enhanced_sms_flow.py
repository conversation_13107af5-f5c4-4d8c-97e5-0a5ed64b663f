#!/usr/bin/env python3
"""
Test Enhanced SMS Flow with Central Intelligence Layer
Tests the enhanced AgentOrchestrator features in the SMS chatbot
"""

import requests
import json
from datetime import datetime
import time

def test_enhanced_sms_flow():
    """Test the enhanced SMS conversation flow with central intelligence layer"""
    
    webhook_url = "http://localhost:8000/api/webhooks/webhooks/kudosity"
    
    # Use a fresh phone number for testing enhanced features
    test_phone = "3030303030"
    
    # Test messages that will demonstrate enhanced orchestrator features
    test_messages = [
        # Stage 1: Initial Greeting (should work normally)
        {
            "message": "Hello!",
            "expected_behavior": "Normal greeting response",
            "description": "🚀 Initial greeting - should work with enhanced orchestrator"
        },
        
        # Stage 2: Invalid name input (should get educational response)
        {
            "message": "123",
            "expected_behavior": "Educational response about proper name format",
            "description": "🎓 Invalid name input - should trigger educational response"
        },
        
        # Stage 3: Valid name input (should process normally)
        {
            "message": "<PERSON> Thompson",
            "expected_behavior": "Process name and move to next question",
            "description": "👤 Valid name input - should process through pipeline"
        },
        
        # Stage 4: Valid budget response
        {
            "message": "C",
            "expected_behavior": "Process budget and continue qualification",
            "description": "💰 Budget response - should process and continue"
        },
        
        # Stage 5: Valid experience response
        {
            "message": "Yes",
            "expected_behavior": "Process experience and continue",
            "description": "📋 Experience response - should process normally"
        },
        
        # Stage 6: Valid owner/investor response (should complete qualification)
        {
            "message": "A",
            "expected_behavior": "Complete qualification and move to document Q&A",
            "description": "🏢 Owner/investor response - should complete qualification"
        },
        
        # Stage 7: Off-topic question (should get educational response)
        {
            "message": "What's the weather like today?",
            "expected_behavior": "Educational response about franchise-related topics",
            "description": "🌤️ Off-topic question - should trigger educational response"
        },
        
        # Stage 8: Valid franchise question (should get RAG answer)
        {
            "message": "What is the initial investment required?",
            "expected_behavior": "RAG answer with placeholder resolution",
            "description": "❓ Valid franchise question - should get enhanced RAG answer"
        },
        
        # Stage 9: End conversation
        {
            "message": "done",
            "expected_behavior": "Personalized goodbye with placeholders resolved",
            "description": "👋 End conversation - should get enhanced goodbye message"
        }
    ]
    
    print(f"🤖 ENHANCED SMS FLOW TEST - Phone: {test_phone}")
    print("=" * 80)
    print("🧠 Testing Central Intelligence Layer Features:")
    print("   ✅ Dynamic Placeholder Resolution")
    print("   ✅ Context Management and User Education") 
    print("   ✅ Unified Message Pipeline")
    print("=" * 80)
    
    for i, test_case in enumerate(test_messages, 1):
        message = test_case["message"]
        expected_behavior = test_case["expected_behavior"]
        description = test_case["description"]
        
        print(f"\n{description}")
        print(f"📝 Message: '{message}'")
        print(f"🎯 Expected: {expected_behavior}")
        
        # Create webhook payload
        payload = {
            "event_type": "SMS_INBOUND",
            "timestamp": datetime.now().isoformat() + "Z",
            "mo": {
                "type": "SMS",
                "id": f"enhanced_test_{i:03d}",
                "message": message,
                "recipient": "1234567890",
                "sender": test_phone,
                "routed_via": "1234567890"
            }
        }
        
        try:
            # Send webhook request
            response = requests.post(
                webhook_url,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                response_data = response.json()
                
                # Check if AI response is in the response
                if 'data' in response_data and 'ai_response' in response_data['data']:
                    ai_response = response_data['data']['ai_response']
                    ai_success = response_data['data'].get('ai_success', False)
                    ai_metadata = response_data['data'].get('ai_metadata', {})
                    
                    actual_stage = ai_metadata.get('conversation_stage', 'unknown')
                    session_id = ai_metadata.get('session_id', 'unknown')
                    processing_method = ai_metadata.get('processing_method', 'unknown')
                    
                    print(f"🤖 AI Response: {ai_response[:200]}{'...' if len(ai_response) > 200 else ''}")
                    print(f"✅ Success: {ai_success}")
                    print(f"📊 Stage: {actual_stage}")
                    print(f"🆔 Session: {session_id}")
                    print(f"⚙️  Processing: {processing_method}")
                    
                    # Analyze response for enhanced features
                    if "educational" in expected_behavior.lower():
                        if any(phrase in ai_response.lower() for phrase in ["please provide", "i'm looking for", "help me understand"]):
                            print("🎓 ✅ Educational response detected!")
                        else:
                            print("🎓 ❓ Educational response not clearly detected")
                    
                    if "placeholder" in expected_behavior.lower():
                        if "{{" not in ai_response:
                            print("🔧 ✅ Placeholders appear to be resolved!")
                        else:
                            print("🔧 ❓ Unresolved placeholders detected")
                    
                    # Check for proper formatting (unified pipeline)
                    if ai_response and ai_response[0].isupper() and ai_response.rstrip().endswith(('.', '!', '?')):
                        print("📝 ✅ Proper message formatting detected!")
                    else:
                        print("📝 ❓ Message formatting may need attention")
                        
                else:
                    print("❌ No AI response in webhook response")
                    print(f"📄 Response: {json.dumps(response_data, indent=2)}")
                    
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"📄 Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
            
        print("-" * 60)
        
        # Small delay between messages to simulate real conversation
        time.sleep(2)
    
    print("\n🎉 ENHANCED SMS FLOW TEST COMPLETED!")
    print("\n📊 ENHANCED FEATURES TESTED:")
    print("✅ Dynamic Placeholder Resolution")
    print("✅ Context Validation and User Education")
    print("✅ Unified Message Pipeline Processing")
    print("✅ Enhanced Agent Response Processing")
    print("✅ Intelligent Context Management")
    print("\n🚀 Enhanced AgentOrchestrator is fully operational! 🧠✨")

if __name__ == "__main__":
    test_enhanced_sms_flow()
