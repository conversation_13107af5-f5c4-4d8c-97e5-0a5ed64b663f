#!/usr/bin/env python3
"""
Test Enhanced AgentOrchestrator with Central Intelligence Layer
Demonstrates dynamic placeholder resolution, context management, and unified message pipeline
"""

import asyncio
import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.agents.orchestrator import AgentOrchestrator

async def test_enhanced_orchestrator():
    """Test the enhanced orchestrator features"""
    
    print("🤖 Testing Enhanced AgentOrchestrator")
    print("=" * 60)
    
    try:
        # Initialize orchestrator
        orchestrator = AgentOrchestrator()
        
        print("✅ Enhanced orchestrator initialized successfully!")
        
        # Test 1: Dynamic Placeholder Resolution
        print("\n🔧 Test 1: Dynamic Placeholder Resolution")
        print("-" * 40)
        
        test_context = {
            "session_id": "test_session_001",
            "sender": "1234567890",
            "franchisor_id": "some-uuid",
            "user_name": "<PERSON>"
        }
        
        test_script = "Hello {{user_name}}! Welcome to {{franchisor_name}}. Today is {{current_date}} and it's {{current_time}}."
        
        resolved_script = await orchestrator.process_script_with_placeholders(
            "Welcome Message", test_context
        )
        
        if resolved_script:
            print(f"📝 Original: {test_script}")
            print(f"✨ Resolved: {resolved_script}")
        else:
            print("❌ Script resolution failed")
        
        # Test 2: Context Validation and Education
        print("\n🎓 Test 2: Context Validation and Education")
        print("-" * 40)
        
        test_cases = [
            {
                "input": "123",
                "context": {
                    "conversation_stage": "prequalification",
                    "current_question": "What's your name?",
                    "session_id": "test_session_002"
                },
                "description": "Invalid name input (numbers)"
            },
            {
                "input": "What's the weather like?",
                "context": {
                    "conversation_stage": "document_qa",
                    "session_id": "test_session_003"
                },
                "description": "Off-topic question in document Q&A"
            },
            {
                "input": "John Smith",
                "context": {
                    "conversation_stage": "prequalification",
                    "current_question": "What's your name?",
                    "session_id": "test_session_004"
                },
                "description": "Valid name input"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 Test Case {i}: {test_case['description']}")
            print(f"📝 Input: '{test_case['input']}'")
            
            is_valid, educational_message = await orchestrator._validate_and_educate_context(
                test_case["input"], test_case["context"]
            )
            
            if is_valid:
                print("✅ Input is valid")
            else:
                print("❌ Input is invalid")
                print(f"🎓 Educational Response: {educational_message}")
        
        # Test 3: Unified Message Pipeline
        print("\n🔄 Test 3: Unified Message Pipeline")
        print("-" * 40)
        
        pipeline_test_context = {
            "session_id": "test_session_005",
            "user_name": "Sarah Johnson",
            "conversation_stage": "document_qa"
        }
        
        test_messages = [
            "thank you for the information!",
            "what are the investment requirements",
            "great news about the franchise"
        ]
        
        for message in test_messages:
            print(f"\n📝 Original: '{message}'")
            
            # Test user input processing
            processed_user, should_continue = await orchestrator._process_message_through_pipeline(
                message, pipeline_test_context, is_user_input=True
            )
            print(f"👤 User Input Processed: '{processed_user}' (continue: {should_continue})")
            
            # Test agent response processing
            processed_agent, _ = await orchestrator._process_message_through_pipeline(
                message, pipeline_test_context, is_user_input=False
            )
            print(f"🤖 Agent Response Processed: '{processed_agent}'")
        
        # Test 4: Orchestrator Status
        print("\n📊 Test 4: Orchestrator Status")
        print("-" * 40)
        
        status = orchestrator.get_orchestrator_status()
        print(f"🏗️  Workflow Compiled: {status['workflow_compiled']}")
        print(f"💾 Checkpointer Enabled: {status['checkpointer_enabled']}")
        print(f"🤖 Total Agents: {len(status['agents'])}")
        print(f"🧠 Central Intelligence:")
        print(f"   - Placeholder Cache Size: {status['central_intelligence']['placeholder_cache_size']}")
        print(f"   - Context Validators: {status['central_intelligence']['context_validators']}")
        print(f"   - Educational Responses: {len(status['central_intelligence']['educational_responses'])}")
        print(f"   - Pipeline Enabled: {status['central_intelligence']['pipeline_enabled']}")
        
        # Test 5: Cache Management
        print("\n🗄️  Test 5: Cache Management")
        print("-" * 40)
        
        print(f"Cache size before: {len(orchestrator.placeholder_cache)}")
        orchestrator.clear_placeholder_cache("test_session_001")
        print(f"Cache size after clearing session: {len(orchestrator.placeholder_cache)}")
        orchestrator.clear_placeholder_cache()
        print(f"Cache size after clearing all: {len(orchestrator.placeholder_cache)}")
        
        print("\n🎉 Enhanced orchestrator tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_enhanced_orchestrator())
