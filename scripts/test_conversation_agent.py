#!/usr/bin/env python3
"""
Test script for the conversation agent
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.agents.conversation_agent import ConversationAgent
from app.agents.base import AgentConfig, AgentRole


async def test_conversation_flow():
    """Test the conversation agent flow"""
    print("🤖 Testing Conversation Agent...")
    
    # Create agent config
    config = AgentConfig(
        role=AgentRole.CONVERSATIONAL_CHATBOT,
        name="Test Conversation Agent",
        description="Test conversational chatbot agent"
    )
    
    # Initialize agent
    agent = ConversationAgent(config)
    
    # Test phone number
    test_phone = "+1234567890"
    
    # Test conversation flow
    test_messages = [
        "Hello",
        "John Doe",
        "C",  # $100k-$200k budget
        "Yes",  # Has business experience
        "A",  # Owner-operator
        "What are the franchise fees?",
        "How much training is provided?",
        "No thanks"
    ]
    
    print(f"📱 Starting conversation for phone: {test_phone}")
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n--- Message {i} ---")
        print(f"👤 User: {message}")
        
        # Create state
        state = {
            "user_input": message,
            "context": {"sender": test_phone},
            "messages": []
        }
        
        # Process message
        result = await agent.process_state(state)
        
        # Print response
        if "error" in result:
            print(f"❌ Error: {result['error']}")
        else:
            print(f"🤖 Bot: {result.get('response', 'No response')}")
            if "session_data" in result:
                session_data = result["session_data"]
                print(f"📊 Stage: {session_data.get('stage', 'unknown')}")
        
        # Small delay to simulate real conversation
        await asyncio.sleep(0.5)
    
    print("\n✅ Conversation test completed!")


if __name__ == "__main__":
    asyncio.run(test_conversation_flow())
