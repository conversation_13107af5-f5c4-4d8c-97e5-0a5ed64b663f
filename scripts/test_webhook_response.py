#!/usr/bin/env python3
"""
Test webhook and show AI agent responses clearly
"""

import requests
import json
import time

def test_conversation_webhook():
    """Test the conversation webhook and display responses"""
    base_url = "http://localhost:8000"
    webhook_url = f"{base_url}/api/webhooks/webhooks/kudosity"
    
    # Test conversation flow
    test_messages = [
        {"message": "Hello", "description": "Initial greeting"},
        {"message": "John Doe", "description": "User provides name"},
        {"message": "C", "description": "Budget: $100k-$200k"},
        {"message": "Yes", "description": "Has business experience"},
        {"message": "A", "description": "Owner-operator"},
        {"message": "What are the franchise fees?", "description": "Document Q&A"},
        {"message": "No thanks", "description": "End conversation"}
    ]
    
    phone_number = "+1234567890"
    
    print("🤖 Testing Conversation Agent via Webhook")
    print("=" * 50)
    
    for i, test in enumerate(test_messages, 1):
        print(f"\n--- Test {i}: {test['description']} ---")
        print(f"👤 User: {test['message']}")
        
        # Prepare webhook payload (Kudosity format)
        payload = {
            "event_type": "SMS_INBOUND",
            "timestamp": "2025-07-16T15:30:00Z",
            "mo": {
                "type": "SMS",
                "id": f"msg_{i}_{int(time.time())}",
                "message": test['message'],
                "recipient": "1234567890",  # Your business number
                "sender": phone_number.replace("+", ""),  # Remove + for Kudosity format
                "routed_via": "1234567890"
            }
        }
        
        try:
            # Send request
            response = requests.post(
                webhook_url,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"🤖 AI Agent: {data.get('response', 'No response')}")
                
                # Show additional info
                if 'metadata' in data:
                    metadata = data['metadata']
                    print(f"📊 Stage: {metadata.get('stage', 'unknown')}")
                    print(f"📱 Session: {metadata.get('session_id', 'unknown')}")
                
                if data.get('success'):
                    print("✅ Success")
                else:
                    print("❌ Failed")
                    if 'error' in data:
                        print(f"Error: {data['error']}")
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"Response: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {e}")
        
        # Small delay between messages
        time.sleep(1)
    
    print("\n" + "=" * 50)
    print("🎉 Conversation test completed!")


if __name__ == "__main__":
    test_conversation_webhook()
