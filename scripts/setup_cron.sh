#!/bin/bash

# Setup Zoho CRM Sync Cron Job
# This script sets up a cron job to run Zoho sync every 15 minutes

set -e

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
CRON_SCRIPT="$PROJECT_ROOT/scripts/zoho_sync_cron.py"
PYTHON_PATH="$PROJECT_ROOT/.venv/bin/python"
LOG_FILE="/var/log/zoho_sync_cron.log"
CRON_JOB="*/15 * * * * cd $PROJECT_ROOT && $PYTHON_PATH $CRON_SCRIPT >> $LOG_FILE 2>&1"

echo "Setting up Zoho CRM Sync Cron Job..."
echo "Project Root: $PROJECT_ROOT"
echo "Python Path: $PYTHON_PATH"
echo "Cron Script: $CRON_SCRIPT"
echo "Log File: $LOG_FILE"

# Make the cron script executable
chmod +x "$CRON_SCRIPT"

# Create log file if it doesn't exist
sudo touch "$LOG_FILE"
sudo chmod 666 "$LOG_FILE"

# Check if cron job already exists
if crontab -l 2>/dev/null | grep -q "zoho_sync_cron.py"; then
    echo "Cron job already exists. Removing old one..."
    crontab -l 2>/dev/null | grep -v "zoho_sync_cron.py" | crontab -
fi

# Add the new cron job
echo "Adding cron job to run every 15 minutes..."
(crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -

# Verify the cron job was added
echo "Current crontab:"
crontab -l | grep "zoho_sync_cron.py" || echo "No cron job found!"

echo ""
echo "✅ Cron job setup completed!"
echo ""
echo "The Zoho CRM sync will now run every 15 minutes."
echo "Log file: $LOG_FILE"
echo ""
echo "To monitor the cron job:"
echo "  tail -f $LOG_FILE"
echo ""
echo "To remove the cron job:"
echo "  crontab -l | grep -v 'zoho_sync_cron.py' | crontab -"
echo ""
echo "To test the cron job manually:"
echo "  cd $PROJECT_ROOT && $PYTHON_PATH $CRON_SCRIPT"
