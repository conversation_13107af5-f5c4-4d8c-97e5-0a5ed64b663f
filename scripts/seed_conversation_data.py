#!/usr/bin/env python3
"""
Seed script for conversational chatbot data
Creates sample sales scripts and pre-qualification questions
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.core.database.connection import get_db
from contextlib import asynccontextmanager


@asynccontextmanager
async def get_db_session():
    """Helper to get database session as context manager"""
    async for db in get_db():
        yield db
        break
from app.models.sales_script import SalesScript
from app.models.pre_qualification_question import PreQualificationQuestion


async def seed_sales_scripts():
    """Seed sales script data"""
    scripts = [
        {
            "script_title": "Initial greeting",
            "script_content": "Hi! I'm here to help you explore the franchise opportunity. Let's get started!",
            "script_stage": "initial_greeting",
            "order_sequence": 1
        },
        {
            "script_title": "Qualification Introduction",
            "script_content": "I'd like to ask you a few questions to better understand your interests and see if this franchise opportunity is a good fit for you.",
            "script_stage": "prequalification",
            "order_sequence": 1
        },
        {
            "script_title": "Document Q&A Introduction",
            "script_content": "Great! Now I can answer questions about our franchise opportunity based on our brochure and documentation. What would you like to know?",
            "script_stage": "document_qa",
            "order_sequence": 1
        },
        {
            "script_title": "Goodbye",
            "script_content": "Thank you for your interest in our franchise opportunity! If you have any more questions in the future, feel free to reach out. Have a great day!",
            "script_stage": "goodbye",
            "order_sequence": 1
        }
    ]
    
    async with get_db_session() as db:
        for script_data in scripts:
            # Check if script already exists
            stmt = select(SalesScript.id).where(SalesScript.script_title == script_data['script_title'])
            existing = await db.execute(stmt)
            if not existing.scalar_one_or_none():
                script = SalesScript(**script_data)
                db.add(script)
        
        await db.commit()
        print("✅ Sales scripts seeded successfully")


async def seed_prequalification_questions():
    """Seed pre-qualification questions"""
    questions = [
        {
            "question_text": "What's your name?",
            "question_type": "text",
            "order_sequence": 1,
            "qualification_weight": 0.1,
            "expected_answer_type": "text",
            "is_required": True,
            "passing_criteria": '{"min_length": 2}'
        },
        {
            "question_text": "What's your budget range for this franchise investment? (A) Under $50k (B) $50k-$100k (C) $100k-$200k (D) Over $200k",
            "question_type": "multiple_choice",
            "order_sequence": 2,
            "qualification_weight": 0.4,
            "expected_answer_type": "choice",
            "is_required": True,
            "answer_options": '["A", "B", "C", "D"]',
            "passing_criteria": '{"qualifying_answers": ["B", "C", "D"]}'
        },
        {
            "question_text": "Do you have previous business or management experience? (Yes/No)",
            "question_type": "yes_no",
            "order_sequence": 3,
            "qualification_weight": 0.3,
            "expected_answer_type": "boolean",
            "is_required": True,
            "answer_options": '["Yes", "No"]',
            "passing_criteria": '{"qualifying_answers": ["Yes"]}'
        },
        {
            "question_text": "Are you looking to be an owner-operator or an investor? (A) Owner-operator (B) Investor (C) Both",
            "question_type": "multiple_choice",
            "order_sequence": 4,
            "qualification_weight": 0.2,
            "expected_answer_type": "choice",
            "is_required": True,
            "answer_options": '["A", "B", "C"]',
            "passing_criteria": '{"qualifying_answers": ["A", "C"]}'
        }
    ]
    
    async with get_db_session() as db:
        for question_data in questions:
            # Check if question already exists
            stmt = select(PreQualificationQuestion.id).where(PreQualificationQuestion.order_sequence == question_data['order_sequence'])
            existing = await db.execute(stmt)
            if not existing.scalar_one_or_none():
                question = PreQualificationQuestion(**question_data)
                db.add(question)
        
        await db.commit()
        print("✅ Pre-qualification questions seeded successfully")


async def main():
    """Main seeding function"""
    print("🌱 Seeding conversational chatbot data...")
    
    try:
        await seed_sales_scripts()
        await seed_prequalification_questions()
        print("🎉 All data seeded successfully!")
        
    except Exception as e:
        print(f"❌ Error seeding data: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
