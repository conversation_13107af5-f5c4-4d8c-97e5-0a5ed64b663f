#!/usr/bin/env python3
"""
Simple webhook test with correct format
"""

import requests
import json
from datetime import datetime

def test_webhook():
    """Test webhook with correct Kudosity format"""
    url = "http://localhost:8000/api/webhooks/webhooks/kudosity"
    
    payload = {
        "event_type": "SMS_INBOUND",
        "timestamp": datetime.now().isoformat() + "Z",
        "mo": {
            "type": "SMS",
            "id": "test_msg_001",
            "message": "Hello",
            "recipient": "1234567890",
            "sender": "1234567890",
            "routed_via": "1234567890"
        }
    }
    
    print("🚀 Sending webhook test...")
    print(f"📤 Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(
            url,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"\n📥 Response Status: {response.status_code}")
        print(f"📄 Response Body: {response.text}")
        
        if response.status_code == 200:
            print("✅ Webhook sent successfully!")
            print("👀 Check the server terminal for AI response output!")
        else:
            print("❌ Webhook failed!")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_webhook()
