#!/usr/bin/env python3
"""
Simple test to verify conversation agent basic functionality
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import select
from app.core.database.connection import get_db
from contextlib import asynccontextmanager
from app.models.sales_script import SalesScript
from app.models.pre_qualification_question import PreQualificationQuestion
from app.models.conversation_session import ConversationSession


@asynccontextmanager
async def get_db_session():
    """Helper to get database session as context manager"""
    async for db in get_db():
        yield db
        break


async def test_basic_functionality():
    """Test basic database functionality"""
    print("🔍 Testing basic functionality...")
    
    async with get_db_session() as db:
        # Test sales scripts
        stmt = select(SalesScript).where(SalesScript.script_title == "Initial greeting")
        result = await db.execute(stmt)
        script = result.scalar_one_or_none()
        
        if script:
            print(f"✅ Found sales script: {script.script_title}")
            print(f"   Content: {script.script_content}")
        else:
            print("❌ No sales script found")
        
        # Test pre-qualification questions
        stmt = select(PreQualificationQuestion).where(PreQualificationQuestion.order_sequence == 1)
        result = await db.execute(stmt)
        question = result.scalar_one_or_none()
        
        if question:
            print(f"✅ Found first question: {question.question_text}")
        else:
            print("❌ No questions found")
        
        # Test conversation session
        stmt = select(ConversationSession).where(ConversationSession.phone_number == "+1234567890")
        result = await db.execute(stmt)
        session = result.scalar_one_or_none()
        
        if session:
            print(f"✅ Found conversation session:")
            print(f"   Phone: {session.phone_number}")
            print(f"   Stage: {session.current_stage}")
            print(f"   Questions asked: {session.questions_asked}")
            print(f"   Message count: {session.message_count}")
        else:
            print("❌ No conversation session found")


if __name__ == "__main__":
    asyncio.run(test_basic_functionality())
