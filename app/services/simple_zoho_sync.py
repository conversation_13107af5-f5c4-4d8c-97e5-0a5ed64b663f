"""
Simple Zoho Sync Service
Focused implementation for your exact requirements:
1. Sync button in franchisors module
2. Pull new/updated data from Zoho
3. Push new/updated data to CMS
4. Handle conflicts by latest timestamp
5. Only sync existing DB fields
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_

from app.services.zoho_client import zoho_client
from app.services.zoho_mapper import ZohoDataMapper
from app.models.lead import Lead
from app.models.franchisor import Franchisor
from app.core.logging import logger


class SimpleZohoSync:
    """Simple Zoho sync focused on your requirements"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def sync_with_zoho(self) -> Dict[str, any]:
        """
        Main sync function called by the "Sync with Zoho" button
        """
        try:
            logger.info("Starting Zoho sync from button click")

            # Get fresh access token using the refresh API
            try:
                access_token = await zoho_client.get_access_token()
                logger.info("Successfully obtained access token for sync")
            except Exception as e:
                logger.error(f"Failed to get access token: {e}")
                return {
                    "success": False,
                    "message": f"Authentication failed: {e}",
                    "data": {"errors": [str(e)]}
                }

            results = {
                "franchisors_pulled": 0,
                "franchisors_pushed": 0,
                "franchisors_updated": 0,
                "leads_pulled": 0,
                "leads_pushed": 0,
                "leads_updated": 0,
                "conflicts_resolved": 0,
                "errors": []
            }

            # Step 1: Pull franchisors from Zoho (only pull, no push for franchisors)
            await self._pull_franchisors_from_zoho(results)

            # Step 2: Pull leads from Zoho
            await self._pull_leads_from_zoho(results)

            # Step 3: Push new CMS leads to Zoho
            await self._push_leads_to_zoho(results)
            
            # Commit all changes
            await self.db.commit()
            
            return {
                "success": True,
                "message": "Sync completed successfully",
                "data": results,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Sync failed: {e}")
            await self.db.rollback()
            return {"success": False, "message": f"Sync failed: {e}"}
    
    async def _pull_leads_from_zoho(self, results: Dict) -> None:
        """Pull new and updated leads from Zoho"""
        try:
            # Get all leads from Zoho
            zoho_leads = await zoho_client.get_leads()
            logger.info(f"Retrieved {len(zoho_leads)} leads from Zoho")
            
            for zoho_lead in zoho_leads:
                zoho_id = zoho_lead.get("id")
                if not zoho_id:
                    continue
                
                # Check if lead exists in CMS
                stmt = select(Lead).where(Lead.zoho_lead_id == zoho_id)
                result = await self.db.execute(stmt)
                existing_lead = result.scalar_one_or_none()
                
                if existing_lead:
                    # Handle conflict: Update if Zoho data is newer
                    if await self._should_update_from_zoho(existing_lead, zoho_lead):
                        await self._update_lead_from_zoho(existing_lead, zoho_lead)
                        results["conflicts_resolved"] += 1
                        logger.info(f"Updated lead {existing_lead.id} from Zoho (conflict resolved)")
                else:
                    # Create new lead from Zoho
                    await self._create_lead_from_zoho(zoho_lead)
                    results["leads_pulled"] += 1
                    logger.info(f"Created new lead from Zoho ID {zoho_id}")
                    
        except Exception as e:
            logger.error(f"Failed to pull leads from Zoho: {e}")
            results["errors"].append(f"Pull failed: {e}")
    
    async def _push_leads_to_zoho(self, results: Dict) -> None:
        """Push new CMS leads to Zoho using batch operations"""
        try:
            # Get CMS leads without Zoho ID (newly created in CMS)
            stmt = select(Lead).where(Lead.zoho_lead_id.is_(None))
            result = await self.db.execute(stmt)
            cms_leads = result.scalars().all()

            if not cms_leads:
                logger.info("No new leads to push to Zoho")
                return

            logger.info(f"Found {len(cms_leads)} CMS leads to push to Zoho")

            # Prepare batch data
            batch_data = []
            lead_mapping = {}  # To map batch results back to CMS leads

            for cms_lead in cms_leads:
                # Convert Lead model to dict for mapper
                cms_lead_dict = {
                    "full_name": cms_lead.full_name,
                    "email": cms_lead.email,
                    "phone": cms_lead.phone,
                    "mobile": cms_lead.mobile,
                    "lead_source": cms_lead.lead_source,
                    "status": cms_lead.status,
                    "location": cms_lead.location,
                    "budget_preference": getattr(cms_lead, 'budget_preference', None),
                    "brand_preference": getattr(cms_lead, 'brand_preference', None),
                    "qualification_status": getattr(cms_lead, 'qualification_status', None),
                    "zoho_lead_id": cms_lead.zoho_lead_id
                }

                # Get franchisor name if brand_preference is set
                if cms_lead.brand_preference:
                    franchisor_name = await self._get_franchisor_name_by_id(cms_lead.brand_preference)
                    if franchisor_name:
                        cms_lead_dict["franchisor_name"] = franchisor_name

                # Use the proper mapper to convert to Zoho format
                zoho_data = ZohoDataMapper.cms_lead_to_zoho(cms_lead_dict)
                batch_data.append(zoho_data)

                # Store mapping for later reference
                lead_mapping[len(batch_data) - 1] = cms_lead

            logger.info(f"Sending batch of {len(batch_data)} leads to Zoho")

            # Send batch to Zoho
            batch_results = await zoho_client.create_leads(batch_data)

            # Process results and update CMS leads with Zoho IDs
            for i, result in enumerate(batch_results):
                cms_lead = lead_mapping.get(i)
                if not cms_lead:
                    continue

                if result.get("status") == "success":
                    zoho_id = result.get("details", {}).get("id")
                    if zoho_id:
                        cms_lead.zoho_lead_id = zoho_id
                        results["leads_pushed"] += 1
                        logger.info(f"Successfully pushed lead {cms_lead.full_name} to Zoho with ID {zoho_id}")
                    else:
                        logger.error(f"No Zoho ID returned for lead {cms_lead.full_name}")
                        results["errors"].append(f"No Zoho ID for lead {cms_lead.full_name}")
                else:
                    error_msg = result.get("message", "Unknown error")
                    logger.error(f"Failed to create lead {cms_lead.full_name} in Zoho: {error_msg}")
                    results["errors"].append(f"Failed to create lead {cms_lead.full_name}: {error_msg}")

            logger.info(f"Batch processing completed. Successfully pushed {results['leads_pushed']} leads.")

        except Exception as e:
            logger.error(f"Failed to push leads to Zoho: {e}")
            results["errors"].append(f"Push failed: {e}")
    
    async def _should_update_from_zoho(self, cms_lead: Lead, zoho_lead: Dict) -> bool:
        """Check if CMS lead should be updated from Zoho (conflict resolution)"""
        try:
            # Parse Zoho modified time
            zoho_modified_str = zoho_lead.get("Modified_Time", "")
            if not zoho_modified_str:
                return False
            
            # Handle different datetime formats from Zoho
            zoho_modified = datetime.fromisoformat(zoho_modified_str.replace("Z", "+00:00"))
            cms_modified = cms_lead.updated_at
            
            # Give priority to latest timestamp
            return zoho_modified > cms_modified
            
        except Exception as e:
            logger.error(f"Error comparing timestamps: {e}")
            return False
    
    async def _create_lead_from_zoho(self, zoho_lead: Dict) -> None:
        """Create new CMS lead from Zoho data"""
        try:
            # Map only existing DB fields
            lead_data = {
                "zoho_lead_id": zoho_lead.get("id"),
                "full_name": zoho_lead.get("Full_Name", ""),
                "email": zoho_lead.get("Email"),
                "phone": zoho_lead.get("Mobile") or zoho_lead.get("Phone"),
                "mobile": zoho_lead.get("Mobile") or zoho_lead.get("Phone"),
                "location": zoho_lead.get("City"),
                "lead_source": zoho_lead.get("Lead_Source"),
                "status": ZohoDataMapper._map_zoho_lead_status_to_cms(zoho_lead.get("Lead_Status", "")),
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }

            # Handle franchisor relationship
            if zoho_lead.get("Franchisor"):
                franchisor_id = await self._find_franchisor_by_name(zoho_lead["Franchisor"])
                if franchisor_id:
                    lead_data["brand_preference"] = franchisor_id

            # Remove None values
            lead_data = {k: v for k, v in lead_data.items() if v is not None}

            new_lead = Lead(**lead_data)
            self.db.add(new_lead)

        except Exception as e:
            logger.error(f"Failed to create lead from Zoho: {e}")
            raise
    
    async def _update_lead_from_zoho(self, cms_lead: Lead, zoho_lead: Dict) -> None:
        """Update existing CMS lead with Zoho data"""
        try:
            # Update only existing fields
            if zoho_lead.get("Full_Name"):
                cms_lead.full_name = zoho_lead["Full_Name"]
            if zoho_lead.get("Email"):
                cms_lead.email = zoho_lead["Email"]
            if zoho_lead.get("Mobile") or zoho_lead.get("Phone"):
                cms_lead.phone = zoho_lead.get("Mobile") or zoho_lead.get("Phone")
                cms_lead.mobile = zoho_lead.get("Mobile") or zoho_lead.get("Phone")
            if zoho_lead.get("Lead_Source"):
                cms_lead.lead_source = zoho_lead["Lead_Source"]
            if zoho_lead.get("Lead_Status"):
                cms_lead.status = ZohoDataMapper._map_zoho_lead_status_to_cms(zoho_lead["Lead_Status"])
            if zoho_lead.get("City"):
                cms_lead.location = zoho_lead["City"]

            # Handle franchisor relationship
            if zoho_lead.get("Franchisor"):
                franchisor_id = await self._find_franchisor_by_name(zoho_lead["Franchisor"])
                if franchisor_id:
                    cms_lead.brand_preference = franchisor_id

            cms_lead.updated_at = datetime.utcnow()

        except Exception as e:
            logger.error(f"Failed to update lead from Zoho: {e}")
            raise
    
    async def _push_lead_to_zoho(self, cms_lead: Lead) -> Optional[str]:
        """Push CMS lead to Zoho and return Zoho ID"""
        try:
            # Convert Lead model to dict for mapper
            cms_lead_dict = {
                "full_name": cms_lead.full_name,
                "email": cms_lead.email,
                "phone": cms_lead.phone,
                "mobile": cms_lead.mobile,
                "lead_source": cms_lead.lead_source,
                "status": cms_lead.status,
                "location": cms_lead.location,
                "budget_preference": getattr(cms_lead, 'budget_preference', None),
                "brand_preference": getattr(cms_lead, 'brand_preference', None),
                "qualification_status": getattr(cms_lead, 'qualification_status', None),
                "zoho_lead_id": cms_lead.zoho_lead_id
            }

            # Get franchisor name if brand_preference is set
            if cms_lead.brand_preference:
                franchisor_name = await self._get_franchisor_name_by_id(cms_lead.brand_preference)
                if franchisor_name:
                    cms_lead_dict["franchisor_name"] = franchisor_name

            # Use the proper mapper to convert to Zoho format
            from app.services.zoho_mapper import ZohoDataMapper
            zoho_data = ZohoDataMapper.cms_lead_to_zoho(cms_lead_dict)

            # Debug: Log the data being sent to Zoho
            logger.info(f"Sending data to Zoho for lead {cms_lead.id}: {zoho_data}")

            # Create lead in Zoho
            response = await zoho_client.create_lead(zoho_data)

            if response and response.get("data") and len(response["data"]) > 0:
                return response["data"][0].get("details", {}).get("id")

        except Exception as e:
            logger.error(f"Failed to push lead {cms_lead.id} to Zoho: {e}")

        return None
    

    async def get_sync_status(self) -> Dict[str, any]:
        """Get current sync status"""
        try:
            # Count leads with/without Zoho IDs
            stmt_synced = select(Lead).where(Lead.zoho_lead_id.isnot(None))
            stmt_unsynced = select(Lead).where(Lead.zoho_lead_id.is_(None))
            
            result_synced = await self.db.execute(stmt_synced)
            result_unsynced = await self.db.execute(stmt_unsynced)
            
            synced_count = len(result_synced.scalars().all())
            unsynced_count = len(result_unsynced.scalars().all())
            
            return {
                "success": True,
                "data": {
                    "synced_leads": synced_count,
                    "unsynced_leads": unsynced_count,
                    "total_leads": synced_count + unsynced_count
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get sync status: {e}")
            return {"success": False, "message": f"Failed to get sync status: {e}"}

    async def _pull_franchisors_from_zoho(self, results: Dict) -> None:
        """Pull new and updated franchisors from Zoho - only those with 'Sale Won - 100%' status"""
        try:
            # Get all franchisors from Zoho
            all_zoho_franchisors = await zoho_client.get_franchisor()
            logger.info(f"Retrieved {len(all_zoho_franchisors)} total franchisors from Zoho")

            # Filter only franchisors with "Sale Won - 100%" status
            zoho_franchisors = [
                franchisor for franchisor in all_zoho_franchisors
                if franchisor.get("Sales_Stage") and franchisor.get("Sales_Stage", "").lower() == "sale won - 100%"
            ]
            logger.info(f"Filtered to {len(zoho_franchisors)} franchisors with 'Sale Won - 100%' status")

            for zoho_franchisor in zoho_franchisors:
                zoho_id = zoho_franchisor.get("id")
                if not zoho_id:
                    continue

                # Check if franchisor exists in CMS
                stmt = select(Franchisor).where(Franchisor.zoho_franchisor_id == zoho_id)
                result = await self.db.execute(stmt)
                existing_franchisor = result.scalar_one_or_none()

                # Use the proper mapper to convert from Zoho format
                from app.services.zoho_mapper import ZohoDataMapper
                cms_franchisor_dict = ZohoDataMapper.zoho_franchisor_to_cms(zoho_franchisor)

                if existing_franchisor:
                    # Check if Zoho version is newer
                    zoho_modified = cms_franchisor_dict.get("zoho_modified_at")
                    if zoho_modified and existing_franchisor.updated_at < zoho_modified:
                        # Update existing franchisor with Zoho data
                        for key, value in cms_franchisor_dict.items():
                            if hasattr(existing_franchisor, key) and key not in ['id', 'created_at']:
                                setattr(existing_franchisor, key, value)

                        existing_franchisor.updated_at = datetime.utcnow()
                        results["franchisors_updated"] += 1
                        logger.info(f"Updated existing franchisor from Zoho ID {zoho_id}")
                    else:
                        logger.info(f"Franchisor {zoho_id} already exists and is up to date")
                else:
                    # Create new franchisor from Zoho data
                    new_franchisor = Franchisor(**cms_franchisor_dict)
                    self.db.add(new_franchisor)
                    results["franchisors_pulled"] += 1
                    logger.info(f"Created new franchisor from Zoho ID {zoho_id} with name: {cms_franchisor_dict.get('name', 'N/A')}")

        except Exception as e:
            logger.error(f"Error pulling franchisors from Zoho: {e}")
            results["errors"].append(f"Pull franchisors error: {e}")

    async def _push_franchisors_to_zoho(self, results: Dict) -> None:
        """Push new and updated franchisors to Zoho using batch operations"""
        try:
            # Find CMS franchisors that don't have zoho_franchisor_id (new ones)
            stmt = select(Franchisor).where(
                and_(
                    Franchisor.zoho_franchisor_id.is_(None),
                    Franchisor.is_active == True,
                    Franchisor.is_deleted == False
                )
            )
            result = await self.db.execute(stmt)
            cms_franchisors = result.scalars().all()

            if not cms_franchisors:
                logger.info("No new franchisors to push to Zoho")
                return

            logger.info(f"Found {len(cms_franchisors)} CMS franchisors to push to Zoho")

            # Get existing Zoho franchisors to check for duplicates by name
            try:
                existing_zoho_franchisors = await zoho_client.get_franchisor()
                existing_names = {f.get("Name", "").lower() for f in existing_zoho_franchisors if f.get("Name")}
                logger.info(f"Found {len(existing_names)} existing franchisor names in Zoho")
            except Exception as e:
                logger.warning(f"Could not fetch existing Zoho franchisors: {e}")
                existing_names = set()

            # Prepare batch data, skipping duplicates
            batch_data = []
            franchisor_mapping = {}  # To map batch results back to CMS franchisors
            skipped_duplicates = 0

            for cms_franchisor in cms_franchisors:
                # Check if franchisor name already exists in Zoho
                if cms_franchisor.name.lower() in existing_names:
                    logger.info(f"Skipping franchisor '{cms_franchisor.name}' - already exists in Zoho")
                    skipped_duplicates += 1
                    continue
                # Convert to dict for mapper
                cms_franchisor_dict = {
                    "id": str(cms_franchisor.id),
                    "name": cms_franchisor.name,
                    "contactfirstname": cms_franchisor.contactfirstname,
                    "contactlastname": cms_franchisor.contactlastname,
                    "email": cms_franchisor.email,
                    "phone": cms_franchisor.phone,
                    "region": cms_franchisor.region,
                    "budget": cms_franchisor.budget,
                    "is_active": cms_franchisor.is_active,
                    "created_at": cms_franchisor.created_at,
                    "updated_at": cms_franchisor.updated_at
                }

                # Use the proper mapper to convert to Zoho format
                zoho_data = ZohoDataMapper.cms_franchisor_to_zoho(cms_franchisor_dict)
                batch_data.append(zoho_data)

                # Store mapping for later reference
                franchisor_mapping[len(batch_data) - 1] = cms_franchisor

            if skipped_duplicates > 0:
                logger.info(f"Skipped {skipped_duplicates} franchisors that already exist in Zoho")

            if not batch_data:
                logger.info("No new franchisors to push to Zoho (all were duplicates or already synced)")
                return

            logger.info(f"Sending batch of {len(batch_data)} franchisors to Zoho")

            # Send batch to Zoho
            batch_results = await zoho_client.create_franchisor_batch(batch_data)

            # Process results and update CMS franchisors with Zoho IDs
            for i, result in enumerate(batch_results):
                cms_franchisor = franchisor_mapping.get(i)
                if not cms_franchisor:
                    continue

                if result.get("status") == "success":
                    zoho_id = result.get("details", {}).get("id")
                    if zoho_id:
                        cms_franchisor.zoho_franchisor_id = zoho_id
                        results["franchisors_pushed"] += 1
                        logger.info(f"Successfully pushed franchisor {cms_franchisor.name} to Zoho with ID {zoho_id}")
                    else:
                        logger.error(f"No Zoho ID returned for franchisor {cms_franchisor.name}")
                        results["errors"].append(f"No Zoho ID for franchisor {cms_franchisor.name}")
                elif result.get("code") == "DUPLICATE_DATA":
                    # This shouldn't happen now since we pre-check for duplicates
                    zoho_id = result.get("details", {}).get("id")
                    logger.warning(f"Unexpected duplicate data for franchisor {cms_franchisor.name}, Zoho ID: {zoho_id}")
                    if zoho_id:
                        # Just store the Zoho ID without updating
                        cms_franchisor.zoho_franchisor_id = zoho_id
                        results["franchisors_updated"] += 1
                        logger.info(f"Linked existing franchisor {cms_franchisor.name} to Zoho ID {zoho_id}")
                    else:
                        results["errors"].append(f"Duplicate franchisor {cms_franchisor.name} but no Zoho ID provided")
                else:
                    error_msg = result.get("message", "Unknown error")
                    logger.error(f"Failed to create franchisor {cms_franchisor.name} in Zoho: {error_msg}")
                    results["errors"].append(f"Failed to create franchisor {cms_franchisor.name}: {error_msg}")

            logger.info(f"Batch processing completed. Successfully pushed {results['franchisors_pushed']} franchisors.")

        except Exception as e:
            logger.error(f"Error pushing franchisors to Zoho: {e}")
            results["errors"].append(f"Push franchisors error: {e}")

    async def _find_franchisor_by_name(self, franchisor_name: str) -> Optional[str]:
        """Find franchisor ID by name"""
        try:
            stmt = select(Franchisor).where(
                and_(
                    Franchisor.name.ilike(f"%{franchisor_name}%"),
                    Franchisor.is_active == True,
                    Franchisor.is_deleted == False
                )
            )
            result = await self.db.execute(stmt)
            franchisor = result.scalar_one_or_none()

            if franchisor:
                return str(franchisor.id)
            else:
                logger.warning(f"Franchisor not found for name: {franchisor_name}")
                return None

        except Exception as e:
            logger.error(f"Error finding franchisor by name '{franchisor_name}': {e}")
            return None

    async def _get_franchisor_name_by_id(self, franchisor_id: str) -> Optional[str]:
        """Get franchisor name by ID"""
        try:
            stmt = select(Franchisor).where(Franchisor.id == franchisor_id)
            result = await self.db.execute(stmt)
            franchisor = result.scalar_one_or_none()

            if franchisor:
                return franchisor.name
            else:
                logger.warning(f"Franchisor not found for ID: {franchisor_id}")
                return None

        except Exception as e:
            logger.error(f"Error getting franchisor name for ID '{franchisor_id}': {e}")
            return None


def get_simple_zoho_sync(db: AsyncSession) -> SimpleZohoSync:
    """Dependency injection for SimpleZohoSync"""
    return SimpleZohoSync(db)
