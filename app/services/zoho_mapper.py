"""
Zoho Data Mapper
Maps data between CMS format and Zoho CRM format
"""

from typing import Dict, Optional, Any
from datetime import datetime, timezone
from app.core.logging import logger


class ZohoDataMapper:
    """Maps data between CMS and Zoho CRM formats"""
    
    @staticmethod
    def cms_lead_to_zoho(cms_lead: Dict) -> Dict:
        """Convert CMS lead format to Zoho CRM format"""
        try:
            # Map basic fields
            full_name = cms_lead.get("full_name", "")
            name_parts = full_name.split(" ") if full_name else [""]

            zoho_lead = {
                "First_Name": name_parts[0] if name_parts else "",
                "Last_Name": " ".join(name_parts[1:]) if len(name_parts) > 1 else "Unknown",
                "Email": cms_lead.get("email"),
                "Phone": cms_lead.get("phone"),
                "Mobile": cms_lead.get("mobile"),
                "Lead_Source": cms_lead.get("lead_source", "Website"),
                "Lead_Status": ZohoDataMapper._map_cms_lead_status_to_zoho(cms_lead.get("status", "new")),
                "City": cms_lead.get("location"),
                "Created_Time": ZohoDataMapper._serialize_datetime(cms_lead.get("created_at")),
                "Modified_Time": ZohoDataMapper._serialize_datetime(cms_lead.get("updated_at"))
            }
            
            # Map budget preference
            if cms_lead.get("budget_preference"):
                zoho_lead["Investment_Budget"] = float(cms_lead["budget_preference"])
            
            # Map brand preference (franchisor name)
            if cms_lead.get("franchisor_name"):
                zoho_lead["Franchisor"] = cms_lead["franchisor_name"]
            
            # Add qualification status
            if cms_lead.get("qualification_status"):
                zoho_lead["Qualification_Status"] = cms_lead["qualification_status"]
            
            # Add Zoho ID if updating existing lead
            if cms_lead.get("zoho_lead_id"):
                zoho_lead["id"] = cms_lead["zoho_lead_id"]
            
            # Remove None values
            zoho_lead = {k: v for k, v in zoho_lead.items() if v is not None and v != ""}
            
            return zoho_lead
            
        except Exception as e:
            logger.error(f"Error mapping CMS lead to Zoho format: {e}")
            raise
    
    @staticmethod
    def zoho_lead_to_cms(zoho_lead: Dict) -> Dict:
        """Convert Zoho CRM lead format to CMS format"""
        try:
            # Combine first and last name
            full_name = ""
            if zoho_lead.get("First_Name"):
                full_name = zoho_lead["First_Name"]
            if zoho_lead.get("Last_Name"):
                full_name += f" {zoho_lead['Last_Name']}" if full_name else zoho_lead["Last_Name"]
            
            print("Zoho lead9090: ", zoho_lead)
            cms_lead = {
                "full_name": full_name.strip(),
                "email": zoho_lead.get("Email"),
                "phone": zoho_lead.get("Phone"),
                "mobile": zoho_lead.get("Mobile"),
                "location": zoho_lead.get("City"),
                "lead_source": zoho_lead.get("Lead_Source", "Zoho"),
                "status": ZohoDataMapper._map_zoho_lead_status_to_cms(zoho_lead.get("Lead_Status", "")),
                "qualification_status": zoho_lead.get("Qualification_Status", "new"),
                "zoho_lead_id": zoho_lead.get("id"),
                "created_at": zoho_lead.get("Created_Time"),
                "is_active": True,
                "updated_at": zoho_lead.get("Modified_Time"),


            }
            
            # Map budget preference
            if zoho_lead.get("Investment_Budget"):
                try:
                    cms_lead["budget_preference"] = float(zoho_lead["Investment_Budget"])
                except (ValueError, TypeError):
                    pass
            
            # Map franchisor name (will be converted to ID in sync service)
            if zoho_lead.get("Franchisor"):
                cms_lead["franchisor_name"] = zoho_lead["Franchisor"]
            
            # Parse Zoho timestamps
            if zoho_lead.get("Created_Time"):
                cms_lead["zoho_created_at"] = ZohoDataMapper._parse_zoho_datetime(zoho_lead["Created_Time"])
            
            if zoho_lead.get("Modified_Time"):
                cms_lead["zoho_modified_at"] = ZohoDataMapper._parse_zoho_datetime(zoho_lead["Modified_Time"])
            
            # Remove None values
            cms_lead = {k: v for k, v in cms_lead.items() if v is not None and v != ""}
            
            return cms_lead
            
        except Exception as e:
            logger.error(f"Error mapping Zoho lead to CMS format: {e}")
            raise
    
    @staticmethod
    def _map_cms_lead_status_to_zoho(cms_status: str) -> str:
        """Map CMS lead status to Zoho lead status"""
        status_mapping = {
            "new": "New Lead",
            "contacted": "1st Call - No Answer",
            "qualified": "Qualified",
            "unqualified": "Not Qualified",
            "not_interested": "Not Interested",
            "follow_up": "Follow up Required",
            "callback": "Call Back",
            "junk": "Junk Lead",
            "wrong_number": "Wrong Number",
            "out_of_budget": "Out of Budget",
            "region_not_available": "Region is not available",
            "eoi_sent": "EOI/NDA Sent",
            "eoi_signed": "EOI/NDA Signed",
            "application_signed": "Application Form Signed",
            "deposit_paid": "Deposit Paid",
            "franchise_sold": "Franchise Sold",
            "lost": "Not Interested",
            "converted": "Franchise Sold"
        }
        return status_mapping.get(cms_status.lower() if cms_status else "", "New Lead")
    
    @staticmethod
    def _map_zoho_lead_status_to_cms(zoho_status: str) -> str:
        """Map Zoho lead status to CMS lead status"""
        status_mapping = {
            "new lead": "new",
            "1st call - no answer": "contacted",
            "2nd call - no answer": "contacted",
            "3rd call - no answer": "contacted",
            "qualified": "qualified",
            "not qualified": "unqualified",
            "not interested": "not_interested",
            "follow up required": "follow_up",
            "call back": "callback",
            "junk lead": "junk",
            "wrong number": "wrong_number",
            "out of budget": "out_of_budget",
            "region is not available": "region_not_available",
            "eoi/nda sent": "eoi_sent",
            "eoi/nda signed": "eoi_signed",
            "application form signed": "application_signed",
            "deposit paid": "deposit_paid",
            "franchise sold": "franchise_sold",
            "franchise database": "qualified"
        }
        return status_mapping.get(zoho_status.lower() if zoho_status else "", "new")

    @staticmethod
    def _map_cms_franchisor_status_to_zoho(cms_status: str) -> str:
        """Map CMS franchisor status to Zoho franchisor status"""
        status_mapping = {
            "new": "On the radar - 0%",
            "radar": "On the raddar - 0",
            "intro_call": "Introductory Call - 10%",
            "discovery_call": "Discovery Call",
            "pitch": "PItch/ Proposal - 25%",
            "following_presentation": "Following Up on Presentation",
            "following_up": "Following Up - 35%",
            "negotiation": "Negotiation - 65%",
            "agreement_sent": "Agreement Sent - 75%",
            "agreement_signed": "Agreement Signed",
            "invoice_sent": "Invoice Sent",
            "won": "Sale Won - 100%",
            "lost": "Sale - Lost",
            "diary_followup": "Not this time - in Diary to follow up"
        }
        return status_mapping.get(cms_status.lower() if cms_status else "", "On the radar - 0%")

    @staticmethod
    def _map_zoho_franchisor_status_to_cms(zoho_status: str) -> str:
        """Map Zoho franchisor status to CMS franchisor status"""
        status_mapping = {
            "on the radar - 0%": "radar",
            "on the raddar - 0": "radar",
            "introductory call - 10%": "intro_call",
            "discovery call": "discovery_call",
            "pitch/ proposal - 25%": "pitch",
            "following up on presentation": "following_presentation",
            "following up - 35%": "following_up",
            "negotiation - 65%": "negotiation",
            "agreement sent - 75%": "agreement_sent",
            "agreement signed": "agreement_signed",
            "invoice sent": "invoice_sent",
            "sale won - 100%": "won",
            "sale - lost": "lost",
            "not this time - in diary to follow up": "diary_followup"
        }
        return status_mapping.get(zoho_status.lower() if zoho_status else "", "new")
    
    @staticmethod
    def _parse_zoho_datetime(zoho_datetime: str) -> Optional[datetime]:
        """Parse Zoho datetime string to Python datetime with timezone awareness"""
        try:
            # Zoho typically returns datetime in ISO format
            # Example: "2024-01-15T10:30:00+05:30"
            if zoho_datetime:
                # Handle different timezone formats
                if 'Z' in zoho_datetime:
                    # Replace Z with +00:00 for UTC
                    zoho_datetime = zoho_datetime.replace('Z', '+00:00')

                # Parse with timezone info preserved
                dt = datetime.fromisoformat(zoho_datetime)

                # Convert to UTC if it has timezone info
                if dt.tzinfo is not None:
                    dt = dt.astimezone(timezone.utc)
                else:
                    # If no timezone info, assume UTC
                    dt = dt.replace(tzinfo=timezone.utc)

                return dt
        except Exception as e:
            logger.warning(f"Could not parse Zoho datetime '{zoho_datetime}': {e}")

        return None

    @staticmethod
    def _serialize_datetime(dt) -> Optional[str]:
        """Convert datetime object to ISO string for JSON serialization"""
        if dt is None:
            return None
        if isinstance(dt, datetime):
            return dt.isoformat()
        if isinstance(dt, str):
            return dt
        return None

    @staticmethod
    def cms_franchisor_to_zoho(cms_franchisor: Dict) -> Dict:
        """Convert CMS franchisor format to Zoho CRM format"""
        try:
            zoho_franchisor = {
                "Name": cms_franchisor.get("name", ""),
                "Contact_First_Name": cms_franchisor.get("contactfirstname") or cms_franchisor.get("contactFirstName"),
                "Contact_Last_Name": cms_franchisor.get("contactlastname") or cms_franchisor.get("contactLastName"),
                "Email": cms_franchisor.get("email"),
                "Mobile": cms_franchisor.get("phone"),
                "Billing_State": cms_franchisor.get("region"),
                "Created_Time": ZohoDataMapper._serialize_datetime(cms_franchisor.get("created_at")),
                "Modified_Time": ZohoDataMapper._serialize_datetime(cms_franchisor.get("updated_at")),
                "Type": "Franchisor",  # Default type for franchisors
            }

            # Map region if available
            if cms_franchisor.get("region"):
                zoho_franchisor["Region"] = cms_franchisor["region"]

            # Map budget if available
            if cms_franchisor.get("budget"):
                zoho_franchisor["Budget"] = float(cms_franchisor["budget"])

            # Map status if available
            if cms_franchisor.get("status"):
                zoho_franchisor["Sales_Stage"] = ZohoDataMapper._map_cms_franchisor_status_to_zoho(cms_franchisor["status"])

            # Add Zoho ID if updating existing franchisor
            if cms_franchisor.get("zoho_franchisor_id"):
                zoho_franchisor["id"] = cms_franchisor["zoho_franchisor_id"]

            # Remove None values
            zoho_franchisor = {k: v for k, v in zoho_franchisor.items() if v is not None and v != ""}

            return zoho_franchisor

        except Exception as e:
            logger.error(f"Error mapping CMS franchisor to Zoho format: {e}")
            raise

    @staticmethod
    def zoho_franchisor_to_cms(zoho_franchisor: Dict) -> Dict:
        """Convert Zoho CRM franchisor format to CMS format"""
        try:
            cms_franchisor = {
                "name": zoho_franchisor.get("Name", ""),
                "contactfirstname": zoho_franchisor.get("Contact_First_Name"),
                "contactlastname": zoho_franchisor.get("Contact_Last_Name"),
                "email": zoho_franchisor.get("Email"),
                "phone": zoho_franchisor.get("Mobile"),
                "region": zoho_franchisor.get("Billing_State"),
                "zoho_franchisor_id": zoho_franchisor.get("id"),
                "franchisor_won_id": zoho_franchisor.get("Franchisor_Won_ID"),  # New field
                "is_active": True,
            }

            # Map budget if available
            if zoho_franchisor.get("Budget"):
                try:
                    cms_franchisor["budget"] = float(zoho_franchisor["Budget"])
                except (ValueError, TypeError):
                    pass

            # Note: Franchisor model doesn't have status field, so we skip status mapping

            # Parse Zoho timestamps to datetime objects
            if zoho_franchisor.get("Created_Time"):
                try:
                    cms_franchisor["created_at"] = ZohoDataMapper._parse_zoho_datetime(zoho_franchisor["Created_Time"])
                except Exception as e:
                    logger.warning(f"Failed to parse Created_Time: {e}")
                    cms_franchisor["created_at"] = datetime.now(datetime.timezone.utc)

            if zoho_franchisor.get("Modified_Time"):
                try:
                    cms_franchisor["updated_at"] = ZohoDataMapper._parse_zoho_datetime(zoho_franchisor["Modified_Time"])
                except Exception as e:
                    logger.warning(f"Failed to parse Modified_Time: {e}")
                    cms_franchisor["updated_at"] = datetime.now(datetime.timezone.utc)

            # Remove None values
            cms_franchisor = {k: v for k, v in cms_franchisor.items() if v is not None and v != ""}

            return cms_franchisor

        except Exception as e:
            logger.error(f"Error mapping Zoho franchisor to CMS format: {e}")
            raise

    @staticmethod
    def prepare_sync_data(entity_data: Dict, operation: str) -> Dict:
        """Prepare data for sync logging"""
        return {
            "operation": operation,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": entity_data
        }
