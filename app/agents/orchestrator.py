"""
Enhanced LangGraph-based Agent Orchestrator
Central intelligence layer that intercepts, filters, and routes all communication
with dynamic placeholder resolution, context management, and unified message pipeline
"""

from typing import Dict, Any, Optional, List, Tuple
import structlog
import re
from datetime import datetime

from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.messages import HumanMessage
from langchain_openai import ChatOpenAI
from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession

from .base import AgentState, AgentRole, AgentConfig, BaseAgent, AgentFactory
from app.core.database.connection import get_db
from app.models.sales_script import SalesScript
from app.models.franchisor import Franchisor
from app.models.conversation_session import ConversationSession

logger = structlog.get_logger()


class AgentOrchestrator:
    """
    Enhanced LangGraph-based orchestrator for the multi-agent system
    Acts as central intelligence layer with:
    - Dynamic placeholder resolution
    - Context management and user education
    - Unified message pipeline for all communications
    """

    def __init__(self):
        self.agents: Dict[str, BaseAgent] = {}
        self.graph: Optional[StateGraph] = None
        self.checkpointer = MemorySaver()
        self.llm = ChatOpenAI(model="gpt-4-turbo", temperature=0.1)

        # Central intelligence layer components
        self.placeholder_cache: Dict[str, Any] = {}
        self.context_validators: Dict[str, callable] = {}
        self.educational_responses: Dict[str, str] = {}

        self._initialize_agents()
        self._initialize_context_validators()
        self._initialize_educational_responses()
        self._build_workflow()

        logger.info("Enhanced agent orchestrator initialized with central intelligence layer")
    
    def _initialize_agents(self):
        """Initialize all agents in the system"""
        agent_configs = [
            AgentConfig(
                role=AgentRole.CONVERSATION,
                name="Conversation Agent",
                description="Handle user greetings and general conversation",
                tools=["store_memory", "retrieve_memory", "validate_email", "validate_phone"]
            ),
            AgentConfig(
                role=AgentRole.DOCUMENT_INGESTION,
                name="Document Ingestion Agent", 
                description="Process and ingest documents for knowledge base",
                tools=["ingest_document", "extract_text", "store_memory"]
            ),
            AgentConfig(
                role=AgentRole.QUESTION_ANSWERING,
                name="Question Answering Agent",
                description="Answer questions based on ingested documents",
                tools=["search_documents", "retrieve_memory", "create_communication"]
            ),
            AgentConfig(
                role=AgentRole.LEAD_QUALIFICATION,
                name="Lead Qualification Agent",
                description="Qualify leads through structured questioning",
                tools=["create_lead", "update_lead_status", "get_lead", "create_communication", "validate_lead_data"]
            ),
            AgentConfig(
                role=AgentRole.MEETING_BOOKING,
                name="Meeting Booking Agent",
                description="Book meetings and manage calendar",
                tools=["book_meeting", "check_availability", "cancel_meeting", "update_lead_status"]
            )
        ]
        
        for config in agent_configs:
            agent = AgentFactory.create_agent(config)
            self.agents[config.role] = agent

    def _initialize_context_validators(self):
        """Initialize context validators for different conversation stages"""
        self.context_validators = {
            "prequalification": self._validate_prequalification_context,
            "document_qa": self._validate_document_qa_context,
            "meeting_booking": self._validate_meeting_context,
            "general": self._validate_general_context
        }

    def _initialize_educational_responses(self):
        """Initialize educational responses for context guidance"""
        self.educational_responses = {
            "invalid_prequalification": "I understand you'd like to share that information. Right now, I'm asking about {current_question}. Please provide a response that helps me understand your {question_context}.",
            "invalid_document_qa": "That's an interesting question! However, I can only answer questions related to our franchise opportunity and business information. Please ask about topics like investment requirements, training, support, or business operations.",
            "invalid_meeting_format": "I'd be happy to help you schedule a meeting! Please provide your preferred date and time, or let me know your availability (e.g., 'next Tuesday at 2 PM' or 'weekday mornings').",
            "out_of_scope": "I appreciate your question, but I'm specifically designed to help with franchise-related inquiries. I can assist you with information about investment requirements, training programs, support systems, and scheduling consultations.",
            "unclear_response": "I want to make sure I understand you correctly. Could you please clarify your response? I'm looking for {expected_format}."
        }

    async def _resolve_placeholders(self, message: str, context: Dict[str, Any]) -> str:
        """
        Central placeholder resolution system
        Dynamically replaces placeholders with database values
        """
        if not message or "{{" not in message:
            return message

        try:
            # Extract all placeholders from the message
            placeholders = re.findall(r'\{\{([^}]+)\}\}', message)
            resolved_message = message

            for placeholder in placeholders:
                value = await self._get_placeholder_value(placeholder, context)
                if value is not None:
                    resolved_message = resolved_message.replace(f"{{{{{placeholder}}}}}", str(value))
                # If value is None, keep placeholder as-is

            return resolved_message

        except Exception as e:
            logger.error(f"Error resolving placeholders: {str(e)}")
            return message  # Return original message if resolution fails

    async def _get_placeholder_value(self, placeholder: str, context: Dict[str, Any]) -> Optional[str]:
        """
        Get value for a specific placeholder from database or context
        """
        # Check cache first
        cache_key = f"{placeholder}_{context.get('session_id', 'default')}"
        if cache_key in self.placeholder_cache:
            return self.placeholder_cache[cache_key]

        try:
            async for db in get_db():
                value = None

                # Handle different placeholder types
                if placeholder == "franchisor_name":
                    value = await self._get_franchisor_name(db, context)
                elif placeholder == "user_name":
                    value = await self._get_user_name(db, context)
                elif placeholder == "greeting":
                    value = await self._get_greeting_message(db, context)
                elif placeholder == "current_date":
                    value = datetime.now().strftime("%B %d, %Y")
                elif placeholder == "current_time":
                    value = datetime.now().strftime("%I:%M %p")
                elif placeholder.startswith("script_"):
                    script_name = placeholder.replace("script_", "").replace("_", " ").title()
                    value = await self._get_script_content(db, script_name)
                elif placeholder in context:
                    value = context[placeholder]

                # Cache the value
                if value is not None:
                    self.placeholder_cache[cache_key] = value

                return value

        except Exception as e:
            logger.error(f"Error getting placeholder value for '{placeholder}': {str(e)}")
            return None

    async def _get_franchisor_name(self, db: AsyncSession, context: Dict[str, Any]) -> Optional[str]:
        """Get franchisor name from database"""
        try:
            # Try to get from context first
            if "franchisor_id" in context:
                stmt = select(Franchisor.name).where(Franchisor.id == context["franchisor_id"])
                result = await db.execute(stmt)
                return result.scalar_one_or_none()

            # Default to first active franchisor
            stmt = select(Franchisor.name).where(
                and_(Franchisor.is_active == True, Franchisor.is_deleted == False)
            ).limit(1)
            result = await db.execute(stmt)
            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"Error getting franchisor name: {str(e)}")
            return None

    async def _get_user_name(self, db: AsyncSession, context: Dict[str, Any]) -> Optional[str]:
        """Get user name from conversation session"""
        try:
            phone_number = context.get("sender") or context.get("phone_number")
            if not phone_number:
                return None

            stmt = select(ConversationSession).where(
                ConversationSession.phone_number == phone_number
            )
            result = await db.execute(stmt)
            session = result.scalar_one_or_none()

            if session and session.lead_id:
                # Get name from lead
                from app.models.lead import Lead
                stmt = select(Lead.first_name, Lead.last_name).where(Lead.id == session.lead_id)
                result = await db.execute(stmt)
                lead_data = result.first()
                if lead_data:
                    return f"{lead_data.first_name} {lead_data.last_name}".strip()

            return None

        except Exception as e:
            logger.error(f"Error getting user name: {str(e)}")
            return None

    async def _get_greeting_message(self, db: AsyncSession, context: Dict[str, Any]) -> Optional[str]:
        """Get personalized greeting message"""
        try:
            user_name = await self._get_user_name(db, context)
            if user_name:
                return f"Hello {user_name}!"
            return "Hello!"

        except Exception as e:
            logger.error(f"Error getting greeting message: {str(e)}")
            return "Hello!"

    async def _get_script_content(self, db: AsyncSession, script_title: str) -> Optional[str]:
        """Get sales script content from database"""
        try:
            stmt = select(SalesScript.script_content).where(
                and_(
                    SalesScript.script_title == script_title,
                    SalesScript.is_active == True,
                    SalesScript.is_deleted == False
                )
            )
            result = await db.execute(stmt)
            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"Error getting script content for '{script_title}': {str(e)}")
            return None

    async def _validate_and_educate_context(self, user_input: str, context: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        Validate user input context and provide educational response if needed
        Returns: (is_valid, educational_message)
        """
        try:
            conversation_stage = context.get("conversation_stage", "general")
            validator = self.context_validators.get(conversation_stage, self.context_validators["general"])

            is_valid, validation_context = await validator(user_input, context)

            if not is_valid:
                educational_message = await self._generate_educational_response(
                    validation_context, context
                )
                return False, educational_message

            return True, None

        except Exception as e:
            logger.error(f"Error validating context: {str(e)}")
            return True, None  # Default to valid if validation fails

    async def _validate_prequalification_context(self, user_input: str, context: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """Validate prequalification stage input"""
        current_question = context.get("current_question", "")
        expected_format = context.get("expected_format", "")

        # Check if input is relevant to current question
        if "name" in current_question.lower():
            if len(user_input.strip()) < 2 or user_input.strip().isdigit():
                return False, {
                    "type": "invalid_prequalification",
                    "current_question": "your name",
                    "question_context": "name (first and last name)",
                    "expected_format": "your full name (e.g., 'John Smith')"
                }

        elif "budget" in current_question.lower() or "investment" in current_question.lower():
            valid_responses = ["a", "b", "c", "d"]
            if user_input.lower().strip() not in valid_responses:
                return False, {
                    "type": "invalid_prequalification",
                    "current_question": "your investment budget",
                    "question_context": "budget range",
                    "expected_format": "A, B, C, or D"
                }

        elif "experience" in current_question.lower():
            valid_responses = ["yes", "no", "y", "n"]
            if user_input.lower().strip() not in valid_responses:
                return False, {
                    "type": "invalid_prequalification",
                    "current_question": "your business experience",
                    "question_context": "experience level",
                    "expected_format": "Yes or No"
                }

        return True, {}

    async def _validate_document_qa_context(self, user_input: str, context: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """Validate document Q&A stage input"""
        # Check if question is franchise-related
        franchise_keywords = [
            "franchise", "investment", "cost", "fee", "training", "support",
            "territory", "royalty", "profit", "business", "opportunity",
            "requirements", "qualifications", "process", "timeline"
        ]

        user_lower = user_input.lower()
        if not any(keyword in user_lower for keyword in franchise_keywords):
            # Check if it's a greeting or off-topic
            off_topic_patterns = [
                r'\b(weather|sports|politics|news|personal)\b',
                r'\b(how are you|what\'s up|hello|hi)\b',
                r'\b(recipe|cooking|travel|entertainment)\b'
            ]

            if any(re.search(pattern, user_lower) for pattern in off_topic_patterns):
                return False, {"type": "invalid_document_qa"}

        return True, {}

    async def _validate_meeting_context(self, user_input: str, context: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """Validate meeting booking stage input"""
        # Check for date/time patterns
        time_patterns = [
            r'\b\d{1,2}:\d{2}\b',  # Time format
            r'\b(monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b',  # Days
            r'\b(morning|afternoon|evening)\b',  # Time of day
            r'\b\d{1,2}(st|nd|rd|th)?\b',  # Dates
            r'\b(january|february|march|april|may|june|july|august|september|october|november|december)\b'  # Months
        ]

        user_lower = user_input.lower()
        if not any(re.search(pattern, user_lower) for pattern in time_patterns):
            return False, {"type": "invalid_meeting_format"}

        return True, {}

    async def _validate_general_context(self, user_input: str, context: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """Validate general conversation context"""
        # Check for completely off-topic or inappropriate content
        inappropriate_patterns = [
            r'\b(spam|advertisement|promotion)\b',
            r'\b(offensive|inappropriate)\b'
        ]

        user_lower = user_input.lower()
        if any(re.search(pattern, user_lower) for pattern in inappropriate_patterns):
            return False, {"type": "out_of_scope"}

        return True, {}

    async def _generate_educational_response(self, validation_context: Dict[str, Any], context: Dict[str, Any]) -> str:
        """Generate educational response based on validation context"""
        response_type = validation_context.get("type", "unclear_response")
        template = self.educational_responses.get(response_type, self.educational_responses["unclear_response"])

        # Replace placeholders in educational response
        educational_message = template.format(**validation_context)

        # Apply dynamic placeholder resolution
        return await self._resolve_placeholders(educational_message, context)

    async def _process_message_through_pipeline(self, message: str, context: Dict[str, Any], is_user_input: bool = True) -> Tuple[str, bool]:
        """
        Unified message pipeline that processes all communications
        Returns: (processed_message, should_continue)
        """
        try:
            # Step 1: Context validation and education (for user inputs)
            if is_user_input:
                is_valid, educational_message = await self._validate_and_educate_context(message, context)
                if not is_valid and educational_message:
                    # Return educational message, don't continue to agents
                    return educational_message, False

            # Step 2: Dynamic placeholder resolution (for all messages)
            processed_message = await self._resolve_placeholders(message, context)

            # Step 3: Content personalization
            personalized_message = await self._personalize_message(processed_message, context)

            # Step 4: Final validation and sanitization
            final_message = await self._sanitize_message(personalized_message, context)

            logger.info(f"Message processed through pipeline: {is_user_input=}, original_length={len(message)}, final_length={len(final_message)}")

            return final_message, True

        except Exception as e:
            logger.error(f"Error in message pipeline: {str(e)}")
            return message, True  # Return original message if pipeline fails

    async def _personalize_message(self, message: str, context: Dict[str, Any]) -> str:
        """Add personalization to messages based on context"""
        try:
            # Add user name if available and not already present
            user_name = context.get("user_name")
            if user_name and user_name.lower() not in message.lower():
                # Add personalization for certain message types
                if message.startswith("Thank you") and "!" in message:
                    message = message.replace("Thank you", f"Thank you, {user_name}")
                elif message.startswith("Great") and "!" in message:
                    message = message.replace("Great", f"Great, {user_name}")

            return message

        except Exception as e:
            logger.error(f"Error personalizing message: {str(e)}")
            return message

    async def _sanitize_message(self, message: str, context: Dict[str, Any]) -> str:
        """Final sanitization and formatting of messages"""
        try:
            # Remove excessive whitespace
            message = re.sub(r'\s+', ' ', message).strip()

            # Ensure proper sentence ending
            if message and not message.endswith(('.', '!', '?')):
                message += '.'

            # Ensure proper capitalization
            if message:
                message = message[0].upper() + message[1:] if len(message) > 1 else message.upper()

            return message

        except Exception as e:
            logger.error(f"Error sanitizing message: {str(e)}")
            return message
    
    def _build_workflow(self):
        """Build the LangGraph workflow"""
        # Create the state graph
        workflow = StateGraph(AgentState)
        
        # Add nodes for each agent
        workflow.add_node("router", self._route_request)
        workflow.add_node("conversation", self._conversation_node)
        workflow.add_node("document_ingestion", self._document_ingestion_node)
        workflow.add_node("question_answering", self._question_answering_node)
        workflow.add_node("lead_qualification", self._lead_qualification_node)
        workflow.add_node("meeting_booking", self._meeting_booking_node)
        workflow.add_node("error_handler", self._error_handler_node)
        
        # Set entry point
        workflow.set_entry_point("router")
        
        # Add conditional edges from router
        workflow.add_conditional_edges(
            "router",
            self._determine_next_agent,
            {
                "conversation": "conversation",
                "document_ingestion": "document_ingestion", 
                "question_answering": "question_answering",
                "lead_qualification": "lead_qualification",
                "meeting_booking": "meeting_booking",
                "error": "error_handler",
                "end": END
            }
        )
        
        # Add edges from agents back to router or end
        for agent_name in ["conversation", "document_ingestion", "question_answering", 
                          "lead_qualification", "meeting_booking"]:
            workflow.add_conditional_edges(
                agent_name,
                self._determine_continuation,
                {
                    "continue": "router",
                    "end": END,
                    "error": "error_handler"
                }
            )
        
        # Error handler always goes to end
        workflow.add_edge("error_handler", END)
        
        # Compile the graph
        self.graph = workflow.compile(checkpointer=self.checkpointer)
        
        logger.info("Workflow graph compiled successfully")
    
    async def _route_request(self, state: AgentState) -> AgentState:
        """
        Enhanced router with central intelligence layer
        Processes user input through unified message pipeline before routing
        """
        try:
            user_input = state.get("user_input", "")
            context = state.get("context", {})

            # Step 1: Process user input through unified message pipeline
            processed_input, should_continue = await self._process_message_through_pipeline(
                user_input, context, is_user_input=True
            )

            # If pipeline returned educational message, return it directly
            if not should_continue:
                state["response"] = processed_input
                state["next_action"] = "end"
                state["current_agent"] = "orchestrator_education"
                state["execution_path"] = state.get("execution_path", []) + ["orchestrator_education"]
                logger.info("Returned educational response from orchestrator")
                return state

            # Step 2: Update state with processed input
            state["user_input"] = processed_input
            state["original_input"] = user_input  # Keep original for reference

            # Step 3: Use LLM to classify intent
            intent_prompt = f"""
            Classify the user's intent from the following message: "{processed_input}"

            Available intents:
            - greeting: General greetings, small talk
            - document_question: Questions about documents, brochures, franchise information
            - lead_qualification: Providing personal information, expressing interest in franchise
            - meeting_booking: Requesting meetings, scheduling appointments
            - document_upload: Uploading or processing documents
            - out_of_scope: Questions unrelated to franchising

            Respond with only the intent name.
            """

            response = await self.llm.ainvoke(intent_prompt)
            intent = response.content.strip().lower()

            # Step 4: Update state
            state["intent"] = intent
            state["current_agent"] = "router"
            state["execution_path"] = state.get("execution_path", []) + ["router"]

            logger.info(f"Routed request with intent: {intent} (processed through pipeline)")
            return state
            
        except Exception as e:
            logger.error(f"Error in router: {str(e)}")
            state["error"] = str(e)
            return state
    
    def _determine_next_agent(self, state: AgentState) -> str:
        """Determine which agent should handle the request"""
        intent = state.get("intent", "")
        error = state.get("error")
        
        if error:
            return "error"
        
        intent_mapping = {
            "greeting": "conversation",
            "document_question": "question_answering",
            "lead_qualification": "lead_qualification", 
            "meeting_booking": "meeting_booking",
            "document_upload": "document_ingestion",
            "out_of_scope": "conversation"
        }
        
        return intent_mapping.get(intent, "conversation")
    
    def _determine_continuation(self, state: AgentState) -> str:
        """Determine if workflow should continue or end"""
        error = state.get("error")
        next_action = state.get("next_action")
        
        if error:
            return "error"
        elif next_action == "continue":
            return "continue"
        else:
            return "end"
    
    async def _conversation_node(self, state: AgentState) -> AgentState:
        """Handle conversation agent processing with unified message pipeline"""
        agent = self.agents.get("conversation")
        if agent:
            state["current_agent"] = "conversation"
            processed_state = await agent.process_state(state)

            # Process agent response through unified pipeline
            if "response" in processed_state:
                processed_response, _ = await self._process_message_through_pipeline(
                    processed_state["response"],
                    processed_state.get("context", {}),
                    is_user_input=False
                )
                processed_state["response"] = processed_response

            return processed_state
        return state

    async def _document_ingestion_node(self, state: AgentState) -> AgentState:
        """Handle document ingestion agent processing with unified message pipeline"""
        agent = self.agents.get("document_ingestion")
        if agent:
            state["current_agent"] = "document_ingestion"
            processed_state = await agent.process_state(state)

            # Process agent response through unified pipeline
            if "response" in processed_state:
                processed_response, _ = await self._process_message_through_pipeline(
                    processed_state["response"],
                    processed_state.get("context", {}),
                    is_user_input=False
                )
                processed_state["response"] = processed_response

            return processed_state
        return state

    async def _question_answering_node(self, state: AgentState) -> AgentState:
        """Handle question answering agent processing with unified message pipeline"""
        agent = self.agents.get("question_answering")
        if agent:
            state["current_agent"] = "question_answering"
            processed_state = await agent.process_state(state)

            # Process agent response through unified pipeline
            if "response" in processed_state:
                processed_response, _ = await self._process_message_through_pipeline(
                    processed_state["response"],
                    processed_state.get("context", {}),
                    is_user_input=False
                )
                processed_state["response"] = processed_response

            return processed_state
        return state
    
    async def _lead_qualification_node(self, state: AgentState) -> AgentState:
        """Handle lead qualification agent processing with unified message pipeline"""
        agent = self.agents.get("lead_qualification")
        if agent:
            state["current_agent"] = "lead_qualification"
            processed_state = await agent.process_state(state)

            # Process agent response through unified pipeline
            if "response" in processed_state:
                processed_response, _ = await self._process_message_through_pipeline(
                    processed_state["response"],
                    processed_state.get("context", {}),
                    is_user_input=False
                )
                processed_state["response"] = processed_response

            return processed_state
        return state

    async def _meeting_booking_node(self, state: AgentState) -> AgentState:
        """Handle meeting booking agent processing with unified message pipeline"""
        agent = self.agents.get("meeting_booking")
        if agent:
            state["current_agent"] = "meeting_booking"
            processed_state = await agent.process_state(state)

            # Process agent response through unified pipeline
            if "response" in processed_state:
                processed_response, _ = await self._process_message_through_pipeline(
                    processed_state["response"],
                    processed_state.get("context", {}),
                    is_user_input=False
                )
                processed_state["response"] = processed_response

            return processed_state
        return state

    async def _error_handler_node(self, state: AgentState) -> AgentState:
        """Handle errors in the workflow with unified message pipeline"""
        error = state.get("error", "Unknown error occurred")
        logger.error(f"Workflow error: {error}")

        error_message = "I apologize, but I encountered an error processing your request. Please try again or contact support."

        # Process error message through unified pipeline
        processed_error_message, _ = await self._process_message_through_pipeline(
            error_message,
            state.get("context", {}),
            is_user_input=False
        )

        state["response"] = processed_error_message
        state["next_action"] = "end"

        return state

    def clear_placeholder_cache(self, session_id: Optional[str] = None):
        """Clear placeholder cache for a specific session or all sessions"""
        if session_id:
            # Clear cache for specific session
            keys_to_remove = [key for key in self.placeholder_cache.keys() if key.endswith(f"_{session_id}")]
            for key in keys_to_remove:
                del self.placeholder_cache[key]
            logger.info(f"Cleared placeholder cache for session: {session_id}")
        else:
            # Clear all cache
            self.placeholder_cache.clear()
            logger.info("Cleared all placeholder cache")

    def get_orchestrator_status(self) -> Dict[str, Any]:
        """Get enhanced orchestrator status with central intelligence layer info"""
        base_status = self.get_workflow_status()

        return {
            **base_status,
            "central_intelligence": {
                "placeholder_cache_size": len(self.placeholder_cache),
                "context_validators": list(self.context_validators.keys()),
                "educational_responses": list(self.educational_responses.keys()),
                "pipeline_enabled": True
            }
        }

    async def process_script_with_placeholders(self, script_title: str, context: Dict[str, Any]) -> Optional[str]:
        """
        Utility method to process a specific script with placeholder resolution
        Useful for external systems that need to resolve scripts
        """
        try:
            async for db in get_db():
                script_content = await self._get_script_content(db, script_title)
                if script_content:
                    return await self._resolve_placeholders(script_content, context)
                return None

        except Exception as e:
            logger.error(f"Error processing script '{script_title}': {str(e)}")
            return None
    
    async def process_message(self, message: str, session_id: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a user message through the agent workflow
        """
        try:
            # Initialize state
            initial_state: AgentState = {
                "user_input": message,
                "messages": [HumanMessage(content=message)],
                "session_id": session_id,
                "intent": None,
                "next_action": None,
                "lead_id": None,
                "lead_data": None,
                "lead_status": None,
                "document_id": None,
                "document_content": None,
                "search_results": None,
                "meeting_data": None,
                "availability": None,
                "context": context or {},
                "conversation_history": [],
                "response": None,
                "error": None,
                "metadata": {},
                "current_agent": None,
                "execution_path": [],
                "retry_count": 0
            }
            
            # Execute the workflow
            config = {"configurable": {"thread_id": session_id}}
            result = await self.graph.ainvoke(initial_state, config)
            
            # Return response
            return {
                "success": True,
                "response": result.get("response", "I'm here to help with your franchise questions."),
                "intent": result.get("intent"),
                "lead_id": result.get("lead_id"),
                "next_action": result.get("next_action"),
                "execution_path": result.get("execution_path", []),
                "metadata": result.get("metadata", {})
            }
            
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "response": "I apologize, but I encountered an error. Please try again."
            }
    
    def get_workflow_status(self) -> Dict[str, Any]:
        """Get current status of the workflow and agents"""
        return {
            "agents": {name: agent.get_status() for name, agent in self.agents.items()},
            "workflow_compiled": self.graph is not None,
            "checkpointer_enabled": self.checkpointer is not None
        }
