"""
Base Agent Classes and Interfaces
Core architecture for the multi-agent system using LangGraph
"""

import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, TypedDict
from pydantic import BaseModel, Field
import structlog

from langchain_core.tools import BaseTool
from langchain_openai import ChatOpenAI
from langchain_core.messages import BaseMessage

logger = structlog.get_logger()


class AgentRole(str, Enum):
    """Enumeration of agent roles in the system"""
    ORCHESTRATOR = "orchestrator"
    DOCUMENT_INGESTION = "document_ingestion"
    QUESTION_ANSWERING = "question_answering"
    LEAD_QUALIFICATION = "lead_qualification"
    MEETING_BOOKING = "meeting_booking"
    CONVERSATION = "conversation"
    CONVERSATIONAL_CHATBOT = "conversational_chatbot"


class AgentStatus(str, Enum):
    """Agent execution status"""
    IDLE = "idle"
    PROCESSING = "processing"
    WAITING = "waiting"
    ERROR = "error"
    COMPLETED = "completed"


class AgentConfig(BaseModel):
    """Configuration for agent initialization"""
    role: AgentRole
    name: str
    description: str
    model: str = "gpt-4-turbo"
    temperature: float = 0.1
    max_tokens: int = 1000
    timeout: int = 30
    verbose: bool = True
    memory_enabled: bool = True
    tools: List[str] = Field(default_factory=list)
    prompt_template: Optional[str] = None
    
    class Config:
        use_enum_values = True


class AgentState(TypedDict):
    """
    Shared state structure for LangGraph workflow
    This state is passed between all agents in the workflow
    """
    # Core conversation data
    user_input: str
    messages: List[BaseMessage]
    session_id: str

    # Intent and routing
    intent: Optional[str]
    next_action: Optional[str]

    # Lead management
    lead_id: Optional[str]
    lead_data: Optional[Dict[str, Any]]
    lead_status: Optional[str]

    # Document processing
    document_id: Optional[str]
    document_content: Optional[str]
    search_results: Optional[List[Dict[str, Any]]]

    # Meeting booking
    meeting_data: Optional[Dict[str, Any]]
    availability: Optional[List[Dict[str, Any]]]

    # Context and memory
    context: Dict[str, Any]
    conversation_history: List[Dict[str, Any]]

    # Response and metadata
    response: Optional[str]
    error: Optional[str]
    metadata: Dict[str, Any]

    # Execution tracking
    current_agent: Optional[str]
    execution_path: List[str]
    retry_count: int


class AgentResponse(BaseModel):
    """Standardized agent response structure"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    execution_time: Optional[float] = None
    agent_id: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class BaseAgent(ABC):
    """
    Abstract base class for all agents in the system
    Each agent is implemented as a LangGraph node function
    """

    def __init__(self, config: AgentConfig):
        self.config = config
        self.id = str(uuid.uuid4())
        self.status = AgentStatus.IDLE
        self.created_at = datetime.utcnow()
        self.last_activity = datetime.utcnow()
        self.execution_count = 0
        self.error_count = 0

        # Initialize LLM
        self.llm = ChatOpenAI(
            model=config.model,
            temperature=config.temperature,
            max_tokens=config.max_tokens
        )

        # Initialize tools
        self.tools: List[BaseTool] = []
        self._initialize_tools()

        logger.info(f"Initialized {config.role} agent", agent_id=self.id)
    
    @abstractmethod
    def _initialize_tools(self):
        """Initialize agent-specific tools"""
        pass

    @abstractmethod
    async def process_state(self, state: AgentState) -> AgentState:
        """
        Main processing method for the agent
        Takes current state and returns updated state
        """
        pass

    def get_system_prompt(self) -> str:
        """Get the system prompt for this agent"""
        return f"""You are a {self.config.role.value} agent in a multi-agent system.
        Your role is to {self.config.description}.

        Always be helpful, professional, and focused on your specific responsibilities.
        Use the available tools when needed and update the conversation state appropriately.
        """
    
    async def execute(self, task: str, context: Optional[Dict[str, Any]] = None) -> AgentResponse:
        """
        Execute a task with proper error handling and logging
        """
        start_time = datetime.utcnow()
        self.status = AgentStatus.PROCESSING
        self.execution_count += 1
        
        try:
            logger.info("Executing task", agent_id=self.id, task=task)
            
            # Prepare input data
            input_data = {
                "task": task,
                "context": context or {},
                "agent_id": self.id,
                "timestamp": start_time
            }
            
            # Process the task
            response = await self.process(input_data)
            
            # Calculate execution time
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            response.execution_time = execution_time
            
            self.status = AgentStatus.COMPLETED
            self.last_activity = datetime.utcnow()
            
            logger.info("Task completed successfully", 
                       agent_id=self.id, 
                       execution_time=execution_time)
            
            return response
            
        except Exception as e:
            self.error_count += 1
            self.status = AgentStatus.ERROR
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            logger.error("Task execution failed", 
                        agent_id=self.id, 
                        error=str(e),
                        execution_time=execution_time)
            
            return AgentResponse(
                success=False,
                error=str(e),
                agent_id=self.id,
                execution_time=execution_time
            )
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status and metrics"""
        return {
            "id": self.id,
            "role": self.config.role,
            "status": self.status,
            "created_at": self.created_at,
            "last_activity": self.last_activity,
            "execution_count": self.execution_count,
            "error_count": self.error_count,
            "success_rate": (
                (self.execution_count - self.error_count) / self.execution_count 
                if self.execution_count > 0 else 0
            )
        }
    
    async def health_check(self) -> bool:
        """Perform health check on the agent"""
        try:
            # Test LLM connectivity
            test_response = await self.llm.ainvoke("Health check")
            return bool(test_response.content)
        except Exception as e:
            logger.error("Health check failed", agent_id=self.id, error=str(e))
            return False


class AgentFactory:
    """Factory class for creating agents"""
    
    @staticmethod
    def create_agent(config: AgentConfig) -> BaseAgent:
        """Create an agent based on configuration"""
        from .document_ingestion import DocumentIngestionAgent
        from .question_answering import QuestionAnsweringAgent
        from .lead_qualification import LeadQualificationAgent
        from .meeting_booking import MeetingBookingAgent
        from .conversation import ConversationAgent
        from .conversation_agent import ConversationAgent as ConversationalChatbotAgent
        
        agent_classes = {
            AgentRole.DOCUMENT_INGESTION: DocumentIngestionAgent,
            AgentRole.QUESTION_ANSWERING: QuestionAnsweringAgent,
            AgentRole.LEAD_QUALIFICATION: LeadQualificationAgent,
            AgentRole.MEETING_BOOKING: MeetingBookingAgent,
            AgentRole.CONVERSATION: ConversationAgent,
            AgentRole.CONVERSATIONAL_CHATBOT: ConversationalChatbotAgent,
        }
        
        agent_class = agent_classes.get(config.role)
        if not agent_class:
            raise ValueError(f"Unknown agent role: {config.role}")
        
        return agent_class(config)
