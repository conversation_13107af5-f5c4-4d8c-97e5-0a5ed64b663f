"""
Webhook API endpoints for Kudosity webhook events
Handles all webhook event types and processes them according to business rules
"""

from datetime import datetime
from typing import Dict, Any
from fastapi import APIRouter, Depends, Request, status, HTTPException
from fastapi.responses import JSONResponse
from pydantic import ValidationError
import structlog
import json

from app.schemas.webhook import (
    WebhookRequest,
    WebhookEventType,
    WebhookReceiveResponse,
    WebhookHealthResponse
)
# Authentication removed for webhook endpoints
from app.core.responses import create_success_response, create_error_response, ErrorCodes
from app.core.logging import logger
from docqa.central_api import ask_question
# Franchisor detection removed - using hardcoded Coochie Hydrogreen

# Import QnA/RAG functionality
try:
    # Import both standard and brochure-optimized RAG
    from docqa.central_api import ask_question
    from docqa import ask_brochure_question
    QNA_AVAILABLE = True
    BROCHURE_QNA_AVAILABLE = True
    logger.info("QnA/RAG functionality loaded successfully")
    logger.info("Brochure-optimized RAG functionality loaded successfully")
except ImportError as e:
    QNA_AVAILABLE = False
    BROCHURE_QNA_AVAILABLE = False
    logger.warning(f"QnA/RAG functionality not available: {e}")

# Try to import just the standard RAG if brochure-optimized failed
if not QNA_AVAILABLE:
    try:
        from docqa.central_api import ask_question
        QNA_AVAILABLE = True
        BROCHURE_QNA_AVAILABLE = False
        logger.info("Standard QnA/RAG functionality loaded successfully")
        logger.warning("Brochure-optimized RAG functionality not available")
    except ImportError as e:
        QNA_AVAILABLE = False
        BROCHURE_QNA_AVAILABLE = False
        logger.warning(f"Standard QnA/RAG functionality not available: {e}")

# Initialize structured logger
logger = structlog.get_logger(__name__)

router = APIRouter(prefix="/webhooks", tags=["Webhooks"])


async def generate_ai_answer(question: str) -> Dict[str, Any]:
    """
    Generate AI-powered answer using brochure-optimized RAG system for Coochie Hydrogreen

    Args:
        question: The question to answer

    Returns:
        Dict containing answer and metadata
    """
    # Check if any RAG system is available
    if not QNA_AVAILABLE and not BROCHURE_QNA_AVAILABLE:
        return {
            "success": False,
            "answer": "❌ QnA/RAG system is not available. Please check the system configuration.",
            "error": "QnA system not available"
        }

    try:
        # Franchisor ID for Coochie Hydrogreen
        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"

        # Try brochure-optimized RAG first (preferred for company brochures)
        if BROCHURE_QNA_AVAILABLE:
            try:
                logger.info(f"Using brochure-optimized RAG for question: {question[:50]}...")

                # Use brochure-optimized parameters
                result = await ask_brochure_question(
                    question=question,
                    franchisor_id=franchisor_id,
                    top_k=5,                    # Optimized for brochures
                    similarity_threshold=0.4,   # Lower threshold for brochures
                    temperature=0.2,            # Balanced for marketing content
                    include_metadata=True,
                    format='json'
                )

                # Parse JSON result
                try:
                    import json
                    parsed_result = json.loads(result)

                    # Add system info
                    if "metadata" not in parsed_result:
                        parsed_result["metadata"] = {}

                    parsed_result["metadata"]["system_info"] = {
                        "rag_type": "brochure_optimized",
                        "triggered_via": "webhook"
                    }

                    return parsed_result

                except json.JSONDecodeError:
                    # If not JSON, create a simple result
                    return {
                        "success": True,
                        "answer": result,
                        "metadata": {
                            "system_info": {
                                "rag_type": "brochure_optimized",
                                "triggered_via": "webhook"
                            }
                        }
                    }

            except Exception as e:
                logger.warning(f"Brochure-optimized RAG failed, falling back to standard RAG: {e}")
                # Fall back to standard RAG

        # Use standard RAG as fallback
        if QNA_AVAILABLE:
            logger.info(f"Using standard RAG for question: {question[:50]}...")

            # Prepare request for Coochie Hydrogreen
            question_request = {
                "question": question,
                "top_k": 10,  # Increased number of relevant documents to retrieve
                "similarity_threshold": 0.1,  # Extremely low threshold (10%) to find any remotely relevant content
                "franchisor_id": franchisor_id,  # Coochie Hydrogreen
                "processing_options": {
                    "priority": 5,  # Medium priority
                    "triggered_via": "webhook"
                }
            }

            # Use the central RAG system to generate an answer
            result = await ask_question(question_request)

            # Add system info
            if "metadata" not in result:
                result["metadata"] = {}

            result["metadata"]["system_info"] = {
                "rag_type": "standard",
                "triggered_via": "webhook"
            }

            return result

        # If we get here, both RAG systems failed
        return {
            "success": False,
            "answer": "❌ All available RAG systems failed to process your question.",
            "error": "RAG systems unavailable"
        }

    except Exception as e:
        logger.error(f"Failed to generate AI answer for question: {question[:100]}... Error: {e}")
        return {
            "success": False,
            "answer": "❌ Sorry, I encountered an error while processing your question.",
            "error": str(e)
        }


async def process_inbound_message(message: str) -> Dict[str, Any]:
    """
    Process inbound message using brochure-optimized RAG system for Coochie Hydrogreen

    Args:
        message: The inbound message content

    Returns:
        Dict containing the answer and metadata
    """
    try:
        # Use hardcoded Coochie Hydrogreen franchisor info
        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
        franchisor_name = "Coochie Hydrogreen"
        detection_confidence = 1.0  # Always 100% confidence since we're hardcoded

        # Get answer from brochure-optimized RAG system
        answer_result = await generate_ai_answer(message)

        # Enhance the result with Coochie Hydrogreen metadata
        if "metadata" not in answer_result:
            answer_result["metadata"] = {}

        answer_result["metadata"].update({
            "franchisor_info": {
                "franchisor_id": franchisor_id,
                "franchisor_name": franchisor_name,
                "detection_method": "hardcoded_coochie_hydrogreen",
                "detection_confidence": detection_confidence
            },
            "processing_info": {
                "message_length": len(message),
                "processing_method": "brochure_optimized_rag",
                "webhook_type": "sms_inbound"
            }
        })

        # Log the interaction with enhanced details
        logger.info(
            "RAG answer generated with franchisor detection",
            question=message,
            answer=answer_result.get("answer", "No answer generated"),
            sources=answer_result.get("sources", []),
            processing_time=answer_result.get("processing_time_ms"),
            detected_franchisor=franchisor_name,
            detection_confidence=detection_confidence
        )

        return answer_result

    except Exception as e:
        logger.error(
            "Failed to process message with RAG",
            error=str(e),
            question=message,
            exc_info=True
        )
        raise


def print_webhook_beautifully(webhook_data: Dict[str, Any], answer_result: Dict[str, Any] = None) -> None:
    """
    Print webhook data in a beautiful format to the terminal
    
    Args:
        webhook_data: The webhook payload dictionary
        answer_result: Optional answer result from RAG system
    """
    print("\n" + "="*80)
    print("🔔 KUDOSITY WEBHOOK RECEIVED")
    print("="*80)
    
    # Print timestamp
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"📅 Received at: {current_time}")
    
    # Print event type prominently
    event_type = webhook_data.get("event_type", "UNKNOWN")
    print(f"📋 Event Type: {event_type}")
    
    # Print webhook timestamp if available
    webhook_timestamp = webhook_data.get("timestamp")
    if webhook_timestamp:
        print(f"⏰ Webhook Timestamp: {webhook_timestamp}")
    
    print("-" * 80)
    
    # Print specific details based on event type
    if event_type == WebhookEventType.SMS_INBOUND:
        mo = webhook_data.get("mo", {})
        print(f"📨 {event_type} DETAILS:")
        print(f"   Message ID: {mo.get('id', 'N/A')}")
        print(f"   From: {mo.get('sender', 'N/A')}")
        print(f"   To: {mo.get('recipient', 'N/A')}")
        print(f"   Message: {mo.get('message', 'N/A')}")
        
        # Print RAG answer if available
        if answer_result:
            print("\n🤖 AI ANSWER:")
            print("-" * 80)
            print(f"Question: {mo.get('message', 'N/A')}")
            print(f"Answer: {answer_result.get('answer', 'No answer generated')}")

            # Display confidence score
            confidence = answer_result.get('confidence_score', 0.0)
            confidence_percent = confidence * 100
            confidence_level = "High" if confidence >= 0.7 else "Medium" if confidence >= 0.4 else "Low"
            print(f"Confidence: {confidence:.3f} ({confidence_percent:.1f}%) - {confidence_level}")

            if answer_result.get('sources'):
                print(f"\nSources ({len(answer_result.get('sources', []))} found):")
                for i, source in enumerate(answer_result.get('sources', [])[:3], 1):  # Show top 3 sources
                    similarity = source.get('similarity_score', 0.0) if isinstance(source, dict) else 0.0
                    print(f"  {i}. Similarity: {similarity:.3f} - {str(source)[:100]}...")
            print("-" * 80)
    
    print("="*80)
    print("✅ Webhook processing completed")
    print("="*80 + "\n")


@router.post(
    "/kudosity",
    response_model=WebhookReceiveResponse,
    summary="Receive Kudosity Webhook",
    description="Endpoint for receiving and processing Kudosity webhook events (no authentication required)",
    responses={
        200: {
            "description": "Webhook processed successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Webhook Processed",
                            "description": "Webhook event processed successfully"
                        },
                        "data": {
                            "message": "Webhook received and processed successfully",
                            "webhook_id": "123e4567-e89b-12d3-a456-426614174000",
                            "event_type": "SMS_INBOUND"
                        }
                    }
                }
            }
        },
        400: {
            "description": "Invalid webhook payload",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Invalid Webhook",
                            "description": "The webhook payload is invalid or malformed"
                        },
                        "error_code": "VALIDATION_ERROR"
                    }
                }
            }
        },
        401: {
            "description": "Unauthorized - Invalid or missing authentication",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Authentication Required",
                            "description": "Valid authentication credentials are required"
                        },
                        "error_code": "UNAUTHORIZED"
                    }
                }
            }
        },
        500: {
            "description": "Internal server error",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Processing Error",
                            "description": "An error occurred while processing the webhook"
                        },
                        "error_code": "INTERNAL_SERVER_ERROR"
                    }
                }
            }
        }
    }
)
async def receive_webhook(
    request: Request
) -> JSONResponse:
    """
    Process incoming Kudosity webhook events

    Args:
        request: The incoming webhook request

    Returns:
        JSONResponse: Webhook processing result
    """
    print(f"\n🚨 KUDOSITY WEBHOOK ENDPOINT HIT!")
    print(f"📊 Request method: {request.method}")
    print(f"📊 Request URL: {request.url}")
    print(f"⏰ Timestamp: {datetime.now()}")
    try:
        # Get raw payload
        body = await request.body()
        try:
            payload = json.loads(body.decode('utf-8'))
            print(f"📦 Parsed payload: {payload}")
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse webhook JSON: {e}")
            return create_error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                message_title="Invalid JSON",
                message_description="Failed to parse webhook payload JSON",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        
        # Validate webhook payload
        try:
            webhook_data = WebhookRequest(**payload)
        except ValidationError as e:
            logger.error(
                "Invalid webhook payload",
                error=str(e),
                payload=payload,
                exc_info=True
            )
            return create_error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                message_title="Invalid Webhook",
                message_description="The webhook payload is invalid or malformed",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        
        # Log webhook receipt
        logger.info(
            "Webhook received",
            event_type=webhook_data.event_type,
            timestamp=webhook_data.timestamp
        )

        # Debug print to console
        print(f"\n🔔 WEBHOOK RECEIVED: {webhook_data.event_type}")
        if hasattr(webhook_data, 'mo') and webhook_data.mo:
            print(f"📱 SMS Message: {webhook_data.mo.message}")
            print(f"📞 From: {webhook_data.mo.sender}")
        print("Processing...")

        # Initialize answer result
        answer_result = None

        # Process SMS inbound events
        if webhook_data.event_type == WebhookEventType.SMS_INBOUND:
            if not webhook_data.mo or not webhook_data.mo.message:
                return create_error_response(
                    error_code=ErrorCodes.VALIDATION_ERROR,
                    message_title="Invalid SMS Inbound",
                    message_description="Missing message content in SMS inbound webhook",
                    status_code=status.HTTP_400_BAD_REQUEST
                )
            
            # Process message with conversational chatbot agent
            try:
                from app.agents.conversation_agent import ConversationAgent
                from app.agents.base import AgentConfig, AgentRole, AgentState
                from langchain_core.messages import HumanMessage

                # Create conversation agent
                config = AgentConfig(
                    role=AgentRole.CONVERSATIONAL_CHATBOT,
                    name="SMS Conversational Chatbot",
                    description="Handle structured SMS conversation flow"
                )
                agent = ConversationAgent(config)

                # Prepare agent state
                state: AgentState = {
                    "user_input": webhook_data.mo.message,
                    "messages": [HumanMessage(content=webhook_data.mo.message)],
                    "session_id": f"sms_{webhook_data.mo.sender}",
                    "intent": None,
                    "next_action": None,
                    "lead_id": None,
                    "lead_data": None,
                    "lead_status": None,
                    "document_id": None,
                    "document_content": None,
                    "search_results": None,
                    "meeting_data": None,
                    "availability": None,
                    "context": {
                        "platform": "sms",
                        "sender": webhook_data.mo.sender,
                        "recipient": webhook_data.mo.recipient,
                        "webhook_payload": payload
                    },
                    "conversation_history": [],
                    "response": None,
                    "error": None,
                    "metadata": {},
                    "current_agent": None,
                    "execution_path": [],
                    "retry_count": 0
                }

                # Process with conversation agent
                result_state = await agent.process_state(state)

                # Create answer result for terminal display
                answer_result = {
                    "success": not bool(result_state.get("error")),
                    "answer": result_state.get("response", "I'm here to help with your franchise questions."),
                    "error": result_state.get("error"),
                    "metadata": {
                        "conversation_stage": result_state.get("session_data", {}).get("stage"),
                        "session_id": result_state.get("session_data", {}).get("session_id"),
                        "processing_method": "conversational_chatbot",
                        "webhook_type": "sms_inbound"
                    }
                }

                # Print beautifully to terminal
                print_webhook_beautifully(payload, answer_result)

            except Exception as e:
                logger.error(
                    "Failed to process inbound message with conversation agent",
                    error=str(e),
                    message=webhook_data.mo.message,
                    exc_info=True
                )
                # Fallback to simple response
                answer_result = {
                    "success": False,
                    "answer": "I apologize, but I encountered an error. Please try again.",
                    "error": str(e)
                }
                print_webhook_beautifully(payload, answer_result)
        
        # Print AI conversation to terminal
        if answer_result and webhook_data.event_type == WebhookEventType.SMS_INBOUND:
            print("\n" + "="*80)
            print("🤖 AI CONVERSATION")
            print("="*80)
            print(f"📱 User Message: {webhook_data.mo.message}")
            print(f"📞 From: {webhook_data.mo.sender}")
            print(f"🤖 AI Response: {answer_result.get('answer', 'No response')}")
            if answer_result.get('metadata'):
                metadata = answer_result['metadata']
                print(f"📊 Stage: {metadata.get('conversation_stage', 'unknown')}")
                print(f"🆔 Session: {metadata.get('session_id', 'unknown')}")
            print(f"✅ Success: {answer_result.get('success', False)}")
            if answer_result.get('error'):
                print(f"❌ Error: {answer_result['error']}")
            print("="*80 + "\n")

        # Prepare response data with AI result if available
        response_data = WebhookReceiveResponse(
            message="Webhook received and processed successfully",
            webhook_id=getattr(webhook_data, 'webhook_id', None),
            event_type=webhook_data.event_type
        ).model_dump()

        # Add AI response to the response data if it was generated
        if answer_result:
            response_data['ai_response'] = answer_result.get('answer', 'No response')
            response_data['ai_success'] = answer_result.get('success', False)
            if answer_result.get('error'):
                response_data['ai_error'] = answer_result['error']
            if answer_result.get('metadata'):
                response_data['ai_metadata'] = answer_result['metadata']

        # Return webhook response with AI data
        return create_success_response(
            data=response_data,
            message_title="Webhook Processed",
            message_description="Webhook event processed successfully",
            status_code=status.HTTP_200_OK
        )
        
    except Exception as e:
        logger.error(
            "Webhook processing failed",
            error=str(e),
            exc_info=True
        )
        return create_error_response(
            error_code=ErrorCodes.UNKNOWN_ERROR,
            message_title="Processing Error",
            message_description="An error occurred while processing the webhook",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get(
    "/health",
    response_model=WebhookHealthResponse,
    summary="Webhook Health Check",
    description="Check the health status of the webhook service and its dependencies (no authentication required)",
    responses={
        200: {
            "description": "Service health status",
            "content": {
                "application/json": {
                    "example": {
                        "status": "healthy",
                        "qna_available": True,
                        "timestamp": "2024-03-20T10:00:00Z"
                    }
                }
            }
        }
    }
)
async def webhook_health() -> WebhookHealthResponse:
    """
    Check webhook service health status

    Returns:
        WebhookHealthResponse: Health check result
    """
    try:
        # Check standard QnA/RAG system availability
        qna_available = True
        try:
            # Simple test question to verify standard RAG system
            await ask_question({"question": "test", "top_k": 1})
        except Exception as e:
            logger.warning(f"Standard RAG health check failed: {e}")
            qna_available = False

        # Check brochure-optimized RAG system availability
        brochure_qna_available = True
        try:
            # Simple test question to verify brochure RAG system
            if BROCHURE_QNA_AVAILABLE:
                await ask_brochure_question(
                    question="test",
                    top_k=1,
                    similarity_threshold=0.4,
                    format='json'
                )
            else:
                brochure_qna_available = False
        except Exception as e:
            logger.warning(f"Brochure RAG health check failed: {e}")
            brochure_qna_available = False

        # Determine overall RAG status
        any_rag_available = qna_available or brochure_qna_available

        # Enhanced health response with brochure RAG status
        return WebhookHealthResponse(
            status="healthy",
            qna_available=any_rag_available,
            timestamp=datetime.now(),
            details={
                "standard_rag_available": qna_available,
                "brochure_rag_available": brochure_qna_available,
                "preferred_rag": "brochure_optimized" if brochure_qna_available else "standard" if qna_available else "none"
            }
        )

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Failed to determine service health status")
