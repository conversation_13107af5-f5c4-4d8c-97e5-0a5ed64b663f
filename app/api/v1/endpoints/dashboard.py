"""
Dashboard API endpoints
Provides dashboard data including counts and recent activity
"""

import logging
from typing import Annotated, Optional
from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db
from app.core.responses import create_success_response, create_error_response, ErrorCodes
from app.schemas.user import UserBase
from app.schemas.dashboard import (
    DashboardCountsSuccessResponse,
    RecentActivitySuccessResponse
)
from app.services.dashboard_service import DashboardService
from app.core.security.enhanced_auth_middleware import get_current_user
from app.core.utils.exception_manager.custom_exceptions import (
    DatabaseError,
    ValidationError
)
from sqlalchemy.exc import SQLAlchemyError

logger = logging.getLogger(__name__)

router = APIRouter()

# Common response examples for documentation
COMMON_RESPONSES = {
    400: {
        "description": "Bad Request",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Bad Request",
                        "description": "Invalid request parameters"
                    },
                    "data": {},
                    "error_code": 4000
                }
            }
        }
    },
    401: {
        "description": "Unauthorized",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Unauthorized",
                        "description": "Authentication required"
                    },
                    "data": {},
                    "error_code": 2001
                }
            }
        }
    },
    500: {
        "description": "Internal Server Error",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Internal Server Error",
                        "description": "An unexpected error occurred"
                    },
                    "data": {},
                    "error_code": 1000
                }
            }
        }
    }
}


def get_dashboard_service(db: AsyncSession = Depends(get_db)) -> DashboardService:
    """Dependency to get dashboard service"""
    return DashboardService(db)


@router.get(
    "/counts",
    response_model=DashboardCountsSuccessResponse,
    summary="Get Dashboard Counts",
    description="Get total counts for leads, franchisors, SMS, and meetings",
    responses={
        200: {
            "description": "Dashboard counts retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Dashboard Counts Retrieved",
                            "description": "Dashboard counts retrieved successfully"
                        },
                        "data": {
                            "total_leads": 150,
                            "total_franchisors": 45,
                            "total_sms": 0,
                            "total_meetings": 0
                        },
                        "error_code": 0
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def get_dashboard_counts(
    current_user: UserBase = Depends(get_current_user),
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    dashboard_service: DashboardService = Depends(get_dashboard_service)
):
    """
    Get dashboard counts including total leads, franchisors, SMS, and meetings.
    
    This endpoint provides summary statistics for the dashboard:
    - Total leads count (from leads table)
    - Total franchisors count (from franchisors table)
    - Total SMS count (static 0 for now)
    - Total meetings count (static 0 for now)
    
    Requires authentication.
    """
    try:
        logger.info("Getting dashboard counts")
        
        # Get dashboard counts
        counts = await dashboard_service.get_dashboard_counts()
        
        return create_success_response(
            data=counts.model_dump(),
            message_title="Dashboard Counts Retrieved",
            message_description="Dashboard counts retrieved successfully"
        )
        
    except DatabaseError as de:
        logger.error(f"Database error getting dashboard counts: {de}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Database Error",
            message_description=str(de),
            status_code=500
        )
        
    except SQLAlchemyError as db_error:
        logger.error(f"Database error getting dashboard counts: {db_error}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Database Error",
            message_description="Failed to retrieve dashboard counts due to database error",
            status_code=500
        )
        
    except Exception as e:
        logger.error(f"Unexpected error getting dashboard counts: {e}", exc_info=True)
        return create_error_response(
            error_code=ErrorCodes.UNKNOWN_ERROR,
            message_title="Internal Server Error",
            message_description="An unexpected error occurred while retrieving dashboard counts",
            status_code=500
        )


@router.get(
    "/recent-activity",
    response_model=RecentActivitySuccessResponse,
    summary="Get Recent Activity",
    description="Get recent activity including latest lead, franchisor, question, and exception",
    responses={
        200: {
            "description": "Recent activity retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Recent Activity Retrieved",
                            "description": "Recent activity retrieved successfully"
                        },
                        "data": {
                            "latest_lead": {
                                "id": "123e4567-e89b-12d3-a456-426614174000",
                                "full_name": "John Doe",
                                "email": "<EMAIL>",
                                "phone": "+1234567890",
                                "status": "new",
                                "created_at": "2025-07-15T10:30:00Z"
                            },
                            "latest_franchisor": {
                                "id": "123e4567-e89b-12d3-a456-426614174001",
                                "name": "Coffee Club Melbourne",
                                "contactFirstName": "Jane",
                                "contactLastName": "Smith",
                                "email": "<EMAIL>",
                                "region": "Melbourne",
                                "is_active": True,
                                "created_at": "2025-07-15T10:25:00Z"
                            },
                            "latest_question": {
                                "id": "123e4567-e89b-12d3-a456-426614174002",
                                "question_text": "What is the initial investment required?",
                                "category": "Investment",
                                "is_active": True,
                                "created_at": "2025-07-15T10:20:00Z"
                            },
                            "latest_exception": {
                                "id": "123e4567-e89b-12d3-a456-426614174003",
                                "error_type": "ValidationError",
                                "error_message": "Sample exception for dashboard display",
                                "endpoint": "/api/sample/",
                                "user_id": None,
                                "severity": "info",
                                "created_at": "2025-07-15T10:15:00Z"
                            }
                        },
                        "error_code": 0
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def get_recent_activity(
    current_user: UserBase = Depends(get_current_user),
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    dashboard_service: DashboardService = Depends(get_dashboard_service)
):
    """
    Get recent activity including latest records from different modules.
    
    This endpoint provides recent activity data:
    - Latest lead (most recently created lead)
    - Latest franchisor (most recently created franchisor)
    - Latest question (most recently created question)
    - Latest exception (mock data for now)
    
    Requires authentication.
    """
    try:
        logger.info("Getting recent activity")
        
        # Get recent activity
        activity = await dashboard_service.get_recent_activity()
        
        return create_success_response(
            data=activity.model_dump(),
            message_title="Recent Activity Retrieved",
            message_description="Recent activity retrieved successfully"
        )
        
    except DatabaseError as de:
        logger.error(f"Database error getting recent activity: {de}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Database Error",
            message_description=str(de),
            status_code=500
        )
        
    except SQLAlchemyError as db_error:
        logger.error(f"Database error getting recent activity: {db_error}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Database Error",
            message_description="Failed to retrieve recent activity due to database error",
            status_code=500
        )
        
    except Exception as e:
        logger.error(f"Unexpected error getting recent activity: {e}", exc_info=True)
        return create_error_response(
            error_code=ErrorCodes.UNKNOWN_ERROR,
            message_title="Internal Server Error",
            message_description="An unexpected error occurred while retrieving recent activity",
            status_code=500
        )
