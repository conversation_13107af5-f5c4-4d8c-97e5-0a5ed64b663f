"""
Lead model for database operations
"""
import uuid
from sqlalchemy import Column, String, DateTime, Text, Numeric, Integer, ForeignKey, Boolean, func, Float
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.core.database.connection import Base


class Lead(Base):
    __tablename__ = "leads"

    # Core identification fields
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    zoho_lead_id = Column(String(100), unique=True)

    # Name fields
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=True)

    # Contact fields
    phone = Column(String(20), index=True)
    mobile = Column(String(20), index=True)
    email = Column(String(255))
    location = Column(String(255))
    postal_code = Column(String(20))

    # Lead source and status (REQUIRED FOREIGN KEYS)
    lead_source_id = Column(UUID(as_uuid=True), ForeignKey("lead_sources.id"), nullable=True)
    lead_status_id = Column(UUID(as_uuid=True), ForeignKey("lead_statuses.id"), nullable=False)

    # Preferences
    brand_preference = Column(UUID(as_uuid=True), ForeignKey("franchisors.id"), nullable=True)
    budget_preference = Column(Numeric(12, 2))

    # Status fields
    status = Column(String(50), default="new", nullable=False, index=True)

    # Qualification scoring
    total_qualification_score = Column(Integer, default=0)
    max_possible_score = Column(Integer, default=0)
    qualification_percentage = Column(Float, default=0.0)
    is_fully_qualified = Column(Boolean, default=False)
    qualification_level = Column(String(20), default="unqualified")
    qualified_at = Column(DateTime(timezone=True), nullable=True)

    # Franchise-specific fields
    franchise_interested_in = Column(Text)
    looking_for_business_opportunity_since = Column(String(100))
    skills = Column(Text)
    looking_to_be_owner_operator = Column(String(50))
    when_looking_to_start = Column(String(100))
    ethnic_background = Column(String(100))
    funds_to_invest = Column(String(100))
    eoi_nda_link = Column(Text)
    work_background = Column(String(100))
    motivation_to_enquire = Column(String(100))
    funds_available = Column(String(100))
    motivation = Column(Text)
    have_run_business_before = Column(Boolean)
    have_mortgage = Column(Boolean)
    high_net_worth = Column(String(100))

    # System fields
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    is_deleted = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    responses = relationship("LeadResponse", back_populates="lead")
    communications = relationship("Communication", back_populates="lead")
    franchisor = relationship("Franchisor", foreign_keys=[brand_preference], lazy="select")
    lead_source = relationship("LeadSource", foreign_keys=[lead_source_id], lazy="select")
    lead_status = relationship("LeadStatus", foreign_keys=[lead_status_id], lazy="select")

    @property
    def full_name(self) -> str:
        """Computed property for backward compatibility"""
        if self.last_name:
            return f"{self.first_name} {self.last_name}".strip()
        return self.first_name or ""

    @full_name.setter
    def full_name(self, value: str):
        """Setter for full_name to split into first_name and last_name"""
        if value:
            parts = value.strip().split(' ', 1)
            self.first_name = parts[0]
            self.last_name = parts[1] if len(parts) > 1 else ""
        else:
            self.first_name = ""
            self.last_name = ""

    def __repr__(self):
        return f"<Lead(id={self.id}, name={self.full_name}, status={self.qualification_status})>"


class Question(Base):
    __tablename__ = "questions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    franchisor_id = Column(UUID(as_uuid=True), ForeignKey("franchisors.id"), nullable=True)
    question_text = Column(String(255), nullable=True)
    question_internal_text = Column(Text, nullable=False)
    question_type = Column(String(50), nullable=False)
    order_sequence = Column(Integer, nullable=False, default=1)
    is_active = Column(Boolean, default=True, nullable=False)  # active, inactive
    is_deleted = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    franchisor = relationship("Franchisor", lazy="select")
    
    def __repr__(self):
        return f"<Question(id={self.id}, type={self.question_type}, status={self.status})>"


class LeadResponse(Base):
    __tablename__ = "lead_responses"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    lead_id = Column(UUID(as_uuid=True), ForeignKey("leads.id"))
    question_id = Column(UUID(as_uuid=True), ForeignKey("pre_qualification_questions.id"))
    response_text = Column(Text)
    answered_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    lead = relationship("Lead", back_populates="responses")
    question = relationship("PreQualificationQuestion", back_populates="responses")

    def __repr__(self):
        return f"<LeadResponse(id={self.id}, lead_id={self.lead_id}, question_id={self.question_id})>"


class Communication(Base):
    __tablename__ = "communications"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    lead_id = Column(UUID(as_uuid=True), ForeignKey("leads.id"), nullable=False)
    communication_type = Column(String(50), nullable=False)  # email, phone, note, meeting, etc.
    subject = Column(String(255))
    content = Column(Text)
    direction = Column(String(20))  # inbound, outbound, internal
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    lead = relationship("Lead", back_populates="communications")

    def __repr__(self):
        return f"<Communication(id={self.id}, lead_id={self.lead_id}, type={self.communication_type})>"


class LeadSource(Base):
    __tablename__ = "lead_sources"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False, unique=True)
    description = Column(Text)
    is_active = Column(Boolean, default=True, nullable=False)
    is_deleted = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    def __repr__(self):
        return f"<LeadSource(id={self.id}, name={self.name})>"


class LeadStatus(Base):
    __tablename__ = "lead_statuses"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False, unique=True)
    colour = Column(String(7), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_deleted = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    def __repr__(self):
        return f"<LeadStatus(id={self.id}, name={self.name})>"
