"""
Conversation Session Model
SQLAlchemy model for tracking SMS conversation sessions and their state
"""

from datetime import datetime
from typing import Optional
from uuid import UUID, uuid4
from sqlalchemy import String, Boolean, DateTime, Text, Integer, ForeignKey, func
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.core.database.connection import Base


class ConversationSession(Base):
    """Conversation session model for tracking SMS chatbot conversations"""

    __tablename__ = "conversation_sessions"

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PGUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        server_default=func.gen_random_uuid(),
    )

    # Session identification
    phone_number: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        index=True,
        comment="Phone number of the lead/user in the conversation",
    )
    
    session_id: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        unique=True,
        index=True,
        comment="Unique session identifier (e.g., sms_+1234567890)",
    )

    # Lead association
    lead_id: Mapped[Optional[UUID]] = mapped_column(
        PGUUID(as_uuid=True),
        ForeignKey("leads.id"),
        nullable=True,
        index=True,
        comment="Associated lead ID (created during qualification)",
    )

    # Conversation state
    current_stage: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        default="initial_greeting",
        index=True,
        comment="Current stage in conversation flow (initial_greeting, prequalification, document_qa, followup, goodbye)",
    )
    
    current_question_id: Mapped[Optional[UUID]] = mapped_column(
        PGUUID(as_uuid=True),
        ForeignKey("pre_qualification_questions.id"),
        nullable=True,
        comment="Current pre-qualification question being asked",
    )
    
    questions_asked: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        comment="Number of pre-qualification questions asked so far",
    )
    
    questions_answered: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        comment="Number of pre-qualification questions answered",
    )
    
    qualification_score: Mapped[Optional[float]] = mapped_column(
        String(10),  # Using String to store percentage like "85.5%"
        nullable=True,
        comment="Current qualification score as percentage",
    )
    
    is_qualified: Mapped[Optional[bool]] = mapped_column(
        Boolean,
        nullable=True,
        comment="Whether the lead is qualified (null = not yet determined)",
    )

    # Session metadata
    conversation_context: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="JSON context data for the conversation",
    )
    
    last_message_sent: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="Last message sent by the bot",
    )
    
    last_message_received: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="Last message received from the user",
    )
    
    message_count: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        comment="Total number of messages exchanged in this session",
    )

    # Session status
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        comment="Whether this session is currently active",
    )
    
    is_completed: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        comment="Whether this conversation has been completed",
    )
    
    completion_reason: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        comment="Reason for completion (goodbye, timeout, error, etc.)",
    )

    # Timestamps
    started_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="When the conversation session started",
    )
    
    last_activity_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="Last activity timestamp",
    )
    
    completed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="When the conversation was completed",
    )
    
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="Creation timestamp",
    )
    
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="Last update timestamp",
    )

    # Relationships
    lead = relationship("Lead", foreign_keys=[lead_id], lazy="select")
    current_question = relationship("PreQualificationQuestion", foreign_keys=[current_question_id], lazy="select")

    def __repr__(self) -> str:
        return (
            f"<ConversationSession(id={self.id}, phone={self.phone_number}, "
            f"stage={self.current_stage}, active={self.is_active})>"
        )
