"""
DocQA Processing Celery Tasks
Background tasks for franchisor brochure processing
"""

import asyncio
from typing import Optional, Dict, Any

from celery import Task
import structlog

from app.core.celery_app import celery_app
from app.core.config.settings import settings

logger = structlog.get_logger(__name__)


class DocQACallbackTask(Task):
    """Base task class for DocQA processing"""
    
    def on_success(self, retval, task_id, args, kwargs):
        """Called when task succeeds"""
        logger.info("DocQA task completed successfully", 
                   task_id=task_id, result=retval)
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Called when task fails"""
        logger.error("DocQA task failed", 
                    task_id=task_id, error=str(exc), traceback=str(einfo))


@celery_app.task(
    bind=True,
    base=DocQACallbackTask,
    name="app.tasks.docqa_processing.process_docqa_task",
    queue=settings.DOCUMENT_PROCESSING_QUEUE,
    max_retries=settings.DOCUMENT_PROCESSING_MAX_RETRIES,
    default_retry_delay=settings.DOCUMENT_PROCESSING_RETRY_DELAY,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 60}
)
def process_docqa_task(
    self,
    franchisor_id: str,
    brochure_url: str,
    processing_options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Process franchisor brochure with DocQA in background thread
    
    Args:
        franchisor_id: Franchisor ID
        brochure_url: S3 URL of the brochure
        processing_options: Optional processing configuration
        
    Returns:
        Dict with processing results
    """
    try:
        logger.info("Starting DocQA processing task",
                   franchisor_id=franchisor_id,
                   brochure_url=brochure_url,
                   task_id=self.request.id)
        
        # Add task ID to processing options
        processing_options = processing_options or {}
        processing_options["task_id"] = self.request.id

        # Process brochure with DocQA service
        result = asyncio.run(_process_franchisor_brochure(
            franchisor_id=franchisor_id,
            brochure_url=brochure_url,
            processing_options=processing_options
        ))
        
        if result["success"]:
            logger.info("DocQA processing completed successfully",
                       franchisor_id=franchisor_id,
                       task_id=self.request.id)
        else:
            logger.error("DocQA processing failed",
                        franchisor_id=franchisor_id,
                        error=result.get("error", "Unknown error"),
                        task_id=self.request.id)
        
        return result
        
    except Exception as exc:
        logger.error("DocQA processing task failed",
                    franchisor_id=franchisor_id,
                    error=str(exc),
                    task_id=self.request.id)
        
        # Retry if not max retries reached
        if self.request.retries < self.max_retries:
            logger.info("Retrying DocQA processing task",
                       franchisor_id=franchisor_id,
                       retry_count=self.request.retries + 1,
                       task_id=self.request.id)
            raise self.retry(exc=exc)
        
        raise exc


def _update_franchisor_processing_status_sync(
    franchisor_id: str,
    status: str,
    message: str = None,
    error: str = None,
    task_id: str = None
):
    """Update franchisor processing status in database (synchronous version for Celery)"""
    try:
        # For now, just log the status update to avoid database connection issues
        # TODO: Implement proper sync database updates once async context issues are resolved
        logger.info("Franchisor processing status update",
                   franchisor_id=franchisor_id,
                   status=status,
                   message=message,
                   error=error,
                   task_id=task_id)

    except Exception as e:
        logger.error("Error updating franchisor processing status",
                    franchisor_id=franchisor_id,
                    error=str(e))


async def _process_franchisor_brochure(
    franchisor_id: str,
    brochure_url: str,
    processing_options: Dict[str, Any]
) -> Dict[str, Any]:
    """Process franchisor brochure using both DocQA service and Agent system"""
    try:
        logger.info("Starting franchisor brochure processing",
                   franchisor_id=franchisor_id,
                   brochure_url=brochure_url)

        # Update processing status
        _update_franchisor_processing_status_sync(
            franchisor_id=franchisor_id,
            status="processing",
            message="Starting brochure processing",
            task_id=processing_options.get("task_id")
        )

        # Process with DocQA service first
        docqa_success = False
        docqa_result = None
        docqa_error = None

        try:
            from app.services.docqa_integration_service import get_docqa_integration_service

            docqa_service = get_docqa_integration_service()

            # Process brochure with DocQA
            docqa_result = await docqa_service.process_franchisor_brochure(
                franchisor_id=franchisor_id,
                brochure_url=brochure_url
            )

            docqa_success = docqa_result and docqa_result.success
            if not docqa_success:
                docqa_error = "DocQA processing failed or returned no result"

        except Exception as e:
            docqa_error = str(e)
            logger.error("DocQA processing failed",
                        franchisor_id=franchisor_id,
                        error=str(e))

        # Also process with Agent system for enhanced ingestion
        agent_success = False
        agent_error = None

        try:
            # Skip agent processing for now to avoid async context issues
            # This can be re-enabled once agent system is properly configured for Celery
            logger.info("Skipping agent processing in background task",
                       franchisor_id=franchisor_id,
                       reason="async_context_compatibility")
            agent_error = "Agent processing skipped in background task"

            # TODO: Implement agent processing in a way that's compatible with Celery
            # This might involve creating a separate sync wrapper or using a different approach

        except Exception as e:
            agent_error = str(e)
            logger.error("Agent processing failed",
                        franchisor_id=franchisor_id,
                        error=str(e))

        # Determine overall success (prioritize DocQA since agent is temporarily disabled)
        overall_success = docqa_success  # or agent_success (when agent processing is re-enabled)

        # Update final processing status
        if overall_success:
            _update_franchisor_processing_status_sync(
                franchisor_id=franchisor_id,
                status="completed",
                message="Brochure processing completed successfully",
                task_id=processing_options.get("task_id")
            )
        else:
            _update_franchisor_processing_status_sync(
                franchisor_id=franchisor_id,
                status="failed",
                message="Brochure processing failed",
                error=f"DocQA: {docqa_error}, Agent: {agent_error}",
                task_id=processing_options.get("task_id")
            )

        result = {
            "success": overall_success,
            "franchisor_id": franchisor_id,
            "docqa_success": docqa_success,
            "agent_success": agent_success,
            "processing_details": {
                "docqa_chunks_created": getattr(docqa_result, 'chunks_created', 0) if docqa_result else 0,
                "docqa_processing_time": getattr(docqa_result, 'processing_time', None) if docqa_result else None,
                "docqa_error": docqa_error,
                "agent_error": agent_error
            }
        }

        if not overall_success:
            result["error"] = f"Both DocQA and Agent processing failed. DocQA: {docqa_error}, Agent: {agent_error}"

        return result

    except Exception as e:
        logger.error("Franchisor brochure processing failed",
                    franchisor_id=franchisor_id,
                    error=str(e))

        # Update error status
        _update_franchisor_processing_status_sync(
            franchisor_id=franchisor_id,
            status="failed",
            message="Brochure processing failed with exception",
            error=str(e),
            task_id=processing_options.get("task_id")
        )

        return {
            "success": False,
            "error": str(e),
            "franchisor_id": franchisor_id
        }
