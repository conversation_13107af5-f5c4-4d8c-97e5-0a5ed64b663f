"""Standard API response models following the specified format"""

from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field


class MessageModel(BaseModel):
    """Message model for API responses"""
    title: str = Field(default="", description="Message title")
    description: str = Field(..., description="Message description")


class PaginationModel(BaseModel):
    """Pagination model for paginated responses"""
    current_page: int = Field(..., description="Current page number")
    total_pages: int = Field(..., description="Total number of pages")
    items_per_page: int = Field(..., description="Items per page")
    total_items: int = Field(..., description="Total number of items")


class DataModel(BaseModel):
    """Data model for API responses"""
    details: Union[Dict[str, Any], List[Any], str, int, float, bool, None] = Field(
        default=None, description="Main data payload"
    )
    pagination: Optional[PaginationModel] = Field(
        default=None, description="Pagination information"
    )


class StandardResponse(BaseModel):
    """Standard API response format"""
    success: bool = Field(..., description="Success status")
    message: MessageModel = Field(..., description="Response message")
    data: Optional[Union[Dict[str, Any], List[Any], str, int, float, bool]] = Field(default=None, description="Response data")
    error_code: Optional[int] = Field(default=None, description="Error code (4 digits)")


class EncryptedResponse(BaseModel):
    """Encrypted response wrapper"""
    details: str = Field(..., description="Encrypted response string")


# Error code constants
class ErrorCodes:
    """Standard error codes"""
    
    # General Application Errors (1xxx)
    UNKNOWN_ERROR = 1000
    SERVICE_UNAVAILABLE = 1001
    DATABASE_ERROR = 1002
    NOT_FOUND = 1003

    
    # Authentication & Authorization Errors (2xxx)
    INVALID_ACCESS_TOKEN = 2001
    EXPIRED_ACCESS_TOKEN = 2002
    MISSING_ACCESS_TOKEN = 2003
    INVALID_REFRESH_TOKEN = 2004
    EXPIRED_REFRESH_TOKEN = 2005
    MISSING_REFRESH_TOKEN = 2008
    ACCESS_TOKEN_REQUIRED = 2009
    UNHANDLED_TOKEN_VALIDATION = 2010
    TOKEN_GENERATION_ERROR = 2011

    # Enhanced Authentication Errors (2xxx continued)
    AUTHENTICATION_REQUIRED = 2012
    AUTHENTICATION_FAILED = 2013
    INVALID_TOKEN_FORMAT = 2014
    TOKEN_EXPIRED = 2015
    TOKEN_REVOKED = 2016
    MALFORMED_AUTH_HEADER = 2017
    UNSUPPORTED_TOKEN_TYPE = 2018
    
    # CRUD Errors (3xxx)
    CREATION_FAILED = 3000
    DUPLICATE_ENTRY = 3001
    RESOURCE_NOT_FOUND = 3100
    INSUFFICIENT_PERMISSIONS = 3104
    UPDATE_FAILED = 3200
    CONFLICT_WITH_EXISTING = 3202
    DELETE_FAILED = 3300
    DELETE_CONFLICT = 3304
    
    # Validation & Input Errors (4xxx)
    INVALID_INPUT = 4000
    MISMATCH_INPUT_SCHEMA = 4001
    MISSING_REQUIRED_FIELD = 4002
    VALIDATION_ERROR = 4003
    UPLOAD_ERROR = 4004
    
    # Resource-Specific Errors (5xxx)
    ACCOUNT_LOCKED = 5003
    INVALID_USER_CREDENTIALS = 5004
    USER_NOT_FOUND = 5005
    USER_NOT_VERIFIED = 5006
    ACCOUNT_DISABLED = 5007
    EMAIL_ALREADY_VERIFIED = 5008
    EMAIL_TAKEN = 5009
    PASSWORD_POLICY_VIOLATION = 5010
    MOBILE_TAKEN = 5011
    INVALID_OTP = 5012
    EXPIRED_OTP = 5013
    INVALID_RESET_CODE = 5014
    EXPIRED_RESET_CODE = 5015
    ACCOUNT_DELETED = 5012
    ACCOUNT_IN_RESTORATION = 5013
    ACCOUNT_IN_VERIFICATION = 5014
    LOGIN_LIMIT_REACHED = 5015
    
    # Azure Errors (6xxx)
    SECRET_NOT_FOUND = 6001
    EXCEPTION_IN_CREDENTIAL_FETCH = 6002
    
    # Redis CRUD Errors (7xxx)
    REDIS_CONNECTION_ERROR = 7001
    REDIS_TIMEOUT_ERROR = 7002
    REDIS_KEY_NOT_FOUND = 7003
    REDIS_CLIENT_NOT_FOUND = 7004
    REDIS_ERROR = 7005
    REDIS_RESPONSE_ERROR = 7006
    
    # Encryption Decryption Errors (8xxx)
    DATA_ENCRYPT_ERROR_AWS = 8001
    DATA_DECRYPT_ERROR_AWS = 8002
    DATA_ENCRYPT_ERROR_ARGON = 8003
    DATA_DECRYPT_ERROR_ARGON = 8004
    ENCRYPT_DECRYPT_CHOICE_EXCEPTION = 8005
    ENCRYPT_DECRYPT_CHOICE_VALUE_ERROR = 8006


# Error messages mapping
ERROR_MESSAGES = {
    ErrorCodes.UNKNOWN_ERROR: {
        "title": "Unknown Error",
        "description": "An unknown error occurred within the system."
    },
    ErrorCodes.SERVICE_UNAVAILABLE: {
        "title": "Service Unavailable",
        "description": "A required service is unavailable."
    },
    ErrorCodes.DATABASE_ERROR: {
        "title": "Database Error",
        "description": "A database error occurred."
    },
    ErrorCodes.INVALID_ACCESS_TOKEN: {
        "title": "Invalid Access Token",
        "description": "The provided access token is malformed."
    },
    ErrorCodes.EXPIRED_ACCESS_TOKEN: {
        "title": "Expired Access Token",
        "description": "The access token has expired and the request cannot be processed."
    },
    ErrorCodes.MISSING_ACCESS_TOKEN: {
        "title": "Missing Access Token",
        "description": "The request is missing an access token in the Authorization header."
    },
    ErrorCodes.CREATION_FAILED: {
        "title": "Creation Failed",
        "description": "Failed to create the resource due to a server-side error."
    },
    ErrorCodes.DUPLICATE_ENTRY: {
        "title": "Duplicate Entry",
        "description": "A resource already exists with the same unique key."
    },
    ErrorCodes.RESOURCE_NOT_FOUND: {
        "title": "Resource Not Found",
        "description": "The requested resource could not be found."
    },
    ErrorCodes.INVALID_INPUT: {
        "title": "Invalid Input",
        "description": "The input data is invalid or incomplete."
    },
    ErrorCodes.MISSING_REQUIRED_FIELD: {
        "title": "Missing Required Field",
        "description": "A required field is missing in the input."
    },
    ErrorCodes.UPLOAD_ERROR: {
        "title": "Upload Error",
        "description": "Failed to upload file due to a server error."
    },
    ErrorCodes.INVALID_USER_CREDENTIALS: {
        "title": "Invalid Credentials",
        "description": "The provided username or password is incorrect."
    },
    ErrorCodes.USER_NOT_FOUND: {
        "title": "User Not Found",
        "description": "No user was found with the provided identifier."
    },
    ErrorCodes.EMAIL_TAKEN: {
        "title": "Email Already Taken",
        "description": "The chosen email is already in use by another account."
    },
    ErrorCodes.PASSWORD_POLICY_VIOLATION: {
        "title": "Password Policy Violation",
        "description": "The provided password does not meet the security requirements."
    },
    ErrorCodes.MOBILE_TAKEN: {
        "title": "Mobile Number Already Taken",
        "description": "The provided mobile number is already in use by another account."
    },
    ErrorCodes.ACCOUNT_DISABLED: {
        "title": "Account Disabled",
        "description": "Your account has been disabled. Please contact support for assistance."
    },

    # Enhanced Authentication Error Messages
    ErrorCodes.AUTHENTICATION_REQUIRED: {
        "title": "Authentication Required",
        "description": "Access token is required. Please provide a valid Bearer token in the Authorization header."
    },
    ErrorCodes.AUTHENTICATION_FAILED: {
        "title": "Authentication Failed",
        "description": "Authentication failed. Please check your credentials and try again."
    },
    ErrorCodes.INVALID_TOKEN_FORMAT: {
        "title": "Invalid Token Format",
        "description": "The provided token format is invalid. Expected format: 'Bearer <token>'."
    },
    ErrorCodes.TOKEN_EXPIRED: {
        "title": "Token Expired",
        "description": "Your access token has expired. Please login again to get a new token."
    },
    ErrorCodes.TOKEN_REVOKED: {
        "title": "Token Revoked",
        "description": "Your access token has been revoked. Please login again to get a new token."
    },
    ErrorCodes.MALFORMED_AUTH_HEADER: {
        "title": "Malformed Authorization Header",
        "description": "The Authorization header is malformed. Expected format: 'Authorization: Bearer <token>'."
    },
    ErrorCodes.UNSUPPORTED_TOKEN_TYPE: {
        "title": "Unsupported Token Type",
        "description": "Only Bearer tokens are supported. Please provide a valid Bearer token."
    },
    ErrorCodes.VALIDATION_ERROR: {
        "title": "Validation Error",
        "description": "The provided data failed validation requirements."
    }
}
