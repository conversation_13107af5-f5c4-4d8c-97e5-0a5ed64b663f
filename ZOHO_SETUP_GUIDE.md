# Zoho CRM Integration Setup Guide

## Overview
This guide will help you set up bidirectional synchronization between your GrowthHive CMS and Zoho CRM using Option B (Scheduled Sync with Manual Trigger).

## Step 1: Zoho Developer Console Setup

### 1.1 Create Zoho Developer Account
1. Go to [Zoho Developer Console](https://api-console.zoho.com/)
2. Sign in with your Zoho account (or create one if needed)
3. Click **"Add Client"** → **"Server-based Applications"**

### 1.2 Configure Your Application
Fill in the following details:
```
Client Name: GrowthHive CMS Integration
Homepage URL: http://localhost:8000 (or your actual domain)
Authorized Redirect URIs: http://localhost:8000/auth/zoho/callback
```

### 1.3 Get Client Credentials
After creating the app, you'll receive:
- **Client ID**: (copy and save this)
- **Client Secret**: (copy and save this)

### 1.4 Generate Authorization Code
1. Replace `{client_id}` with your actual Client ID in this URL:
```
https://accounts.zoho.com/oauth/v2/auth?scope=ZohoCRM.modules.ALL,ZohoCRM.settings.ALL&client_id=1000.HVQXG72VRJVB5VZI5LDH0RL2W8B7WT&response_type=code&access_type=offline&redirect_uri=http://localhost:8000/api/auth/zoho/callback
```

2. Visit this URL in your browser
3. Grant the required permissions
4. You'll be redirected to: `http://localhost:8000/api/auth/zoho/callback?code=AUTHORIZATION_CODE`
5. Copy the `code` parameter value from the URL

### 1.5 Generate Refresh Token
Use this curl command (replace the placeholders with your actual values):
```bash
curl -X POST https://accounts.zoho.com/oauth/v2/token \
  -d "grant_type=authorization_code" \
  -d "client_id=YOUR_CLIENT_ID" \
  -d "client_secret=YOUR_CLIENT_SECRET" \
  -d "redirect_uri=http://localhost:8000/auth/zoho/callback" \
  -d "code=YOUR_AUTHORIZATION_CODE"
```

**Save the `refresh_token` from the response - you'll need this!**

## Step 2: Environment Configuration

Update your `.env` file with the Zoho credentials:
```env
# Zoho CRM Integration Settings
ZOHO_CLIENT_ID=your_client_id_here
ZOHO_CLIENT_SECRET=your_client_secret_here  
ZOHO_REFRESH_TOKEN=your_refresh_token_here
ZOHO_BASE_URL=https://www.zohoapis.com/crm/v2
ZOHO_AUTH_URL=https://accounts.zoho.com/oauth/v2/token
ZOHO_SYNC_INTERVAL_MINUTES=15
```

## Step 3: Database Setup

Run the migration script to create the required tables:
```bash
# Connect to your PostgreSQL database and run:
psql -U postgres -d growthhive -f migrations/add_zoho_sync_tables.sql
```

Or execute the SQL directly in your database client.

## Step 4: Install Dependencies

Make sure you have the required Python packages:
```bash
pip install aiohttp
```

## Step 5: Restart Your Application

Restart your FastAPI application to load the new configuration:
```bash
# If using uvicorn directly:
uvicorn app.main:app --reload

# Or however you normally start your application
```

## Step 6: Test the Integration

### 6.1 Check Sync Status
```bash
curl -X GET "http://localhost:8000/api/v1/zoho/status" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 6.2 Trigger Manual Sync
```bash
curl -X POST "http://localhost:8000/api/v1/zoho/sync" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Available API Endpoints

### Sync Operations
- `POST /api/v1/zoho/sync` - Full bidirectional sync
- `POST /api/v1/zoho/pull` - Pull data from Zoho to CMS
- `POST /api/v1/zoho/push` - Push data from CMS to Zoho
- `GET /api/v1/zoho/status` - Get sync status and statistics
- `GET /api/v1/zoho/logs` - Get recent sync operation logs

### Frontend Integration
Add a "Sync with Zoho" button in your admin panel:
```javascript
// Example JavaScript for sync button
async function syncWithZoho() {
    try {
        const response = await fetch('/api/v1/zoho/sync', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${userToken}`,
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        if (result.success) {
            alert('Sync started successfully!');
        } else {
            alert('Sync failed: ' + result.message);
        }
    } catch (error) {
        alert('Error: ' + error.message);
    }
}
```

## Data Mapping

### Lead Fields Mapping
| CMS Field | Zoho CRM Field |
|-----------|----------------|
| full_name | First_Name + Last_Name |
| email | Email |
| phone | Phone |
| mobile | Mobile |
| location | City |
| lead_source | Lead_Source |
| status | Lead_Status |
| budget_preference | Investment_Budget |
| brand_preference | Brand_Preference |

### Status Mapping
| CMS Status | Zoho Status |
|------------|-------------|
| new | Not Contacted |
| contacted | Contacted |
| qualified | Qualified |
| unqualified | Unqualified |
| converted | Converted |
| lost | Lost Lead |

## Monitoring and Troubleshooting

### Check Sync Logs
```bash
curl -X GET "http://localhost:8000/api/v1/zoho/logs?limit=20" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Common Issues

1. **Authentication Errors**: Check if your refresh token is valid
2. **Rate Limiting**: Zoho has API rate limits - sync will automatically handle this
3. **Data Validation**: Check logs for field mapping issues

### Log Files
Check your application logs for detailed sync information:
```bash
tail -f logs/growthhive.log | grep -i zoho
```

## Security Considerations

1. **Keep credentials secure**: Never commit Zoho credentials to version control
2. **Use HTTPS**: Always use HTTPS in production
3. **Token rotation**: Refresh tokens can expire - monitor and refresh as needed
4. **Access control**: Ensure only authorized users can trigger sync operations

## Scheduled Sync (Optional)

To set up automatic scheduled sync, you can use a cron job:
```bash
# Add to crontab to sync every 15 minutes
*/15 * * * * curl -X POST "http://localhost:8000/api/v1/zoho/sync" -H "Authorization: Bearer YOUR_SERVICE_TOKEN"
```

## Support

If you encounter issues:
1. Check the application logs
2. Verify Zoho API credentials
3. Test API endpoints manually
4. Check database connectivity
5. Review field mappings

The sync system is designed to be resilient and will log all operations for troubleshooting.
