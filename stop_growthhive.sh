#!/bin/bash
"""
GrowthHive Stop Script
Stops all services and Docker containers
"""

set -e

echo "🛑 Stopping GrowthHive Application and All Services"
echo "================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Step 1: Stop FastAPI server (if running)
print_info "Stopping FastAPI server..."
pkill -f "python start_server.py" 2>/dev/null || true
pkill -f "uvicorn" 2>/dev/null || true
print_status "FastAPI server stopped"

# Step 2: Stop Celery workers
print_info "Stopping Celery workers..."
pkill -f "celery_worker.py" 2>/dev/null || true
pkill -f "celery worker" 2>/dev/null || true
print_status "Celery workers stopped"

# Step 3: Stop Docker containers
print_info "Stopping Docker containers..."
docker-compose -f docker-compose.rabbitmq.yml down --remove-orphans 2>/dev/null || true
print_status "Docker containers stopped"

# Step 4: Clean up any remaining processes
print_info "Cleaning up remaining processes..."
pkill -f "growthhive" 2>/dev/null || true

# Step 5: Show final status
print_info "Checking remaining processes..."
REMAINING_PROCESSES=$(ps aux | grep -E "(celery|uvicorn|start_server|growthhive)" | grep -v grep | wc -l)

if [ "$REMAINING_PROCESSES" -eq 0 ]; then
    print_status "All GrowthHive processes stopped successfully"
else
    print_warning "$REMAINING_PROCESSES processes may still be running"
    print_info "You can check with: ps aux | grep -E '(celery|uvicorn|start_server|growthhive)'"
fi

# Step 6: Show Docker status
print_info "Docker containers status:"
docker-compose -f docker-compose.rabbitmq.yml ps

print_status "GrowthHive shutdown completed"
