#!/usr/bin/env python3
"""
Test industry filtering for franchisor listing
"""

import asyncio
import httpx
import json
import uuid

BASE_URL = "http://localhost:8000"

async def test_industry_filter():
    async with httpx.AsyncClient() as client:
        # 1. Register and login
        user_email = f"test_{str(uuid.uuid4())[:8]}@example.com"
        register_data = {
            "email": user_email,
            "password": "TestPassword123!",
            "confirm_password": "TestPassword123!",
            "first_name": "Test",
            "last_name": "User",
            "mobile": f"+{str(uuid.uuid4().int)[:10]}"
        }
        
        print("1. Registering and logging in...")
        response = await client.post(f"{BASE_URL}/api/auth/register", json=register_data)
        if response.status_code != 201:
            print(f"❌ Registration failed: {response.status_code}")
            return
        
        login_data = {
            "email_or_mobile": user_email,
            "password": "TestPassword123!"
        }
        
        response = await client.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if response.status_code != 200:
            print(f"❌ Login failed: {response.status_code}")
            return
        
        data = response.json()
        access_token = data["data"]["details"]["access_token"]
        headers = {"Authorization": f"Bearer {access_token}"}
        print("✅ Login successful")
        
        # 2. Get list of industries first
        print("2. Getting industries...")
        response = await client.get(f"{BASE_URL}/api/industries", headers=headers)
        if response.status_code == 200:
            industries_data = response.json()
            industries = industries_data.get("data", {}).get("items", [])
            if industries:
                test_industry = industries[0]
                industry_id = test_industry["id"]
                industry_name = test_industry["name"]
                print(f"✅ Found test industry: {industry_name} (ID: {industry_id})")
            else:
                print("⚠️ No industries found, creating a test industry...")
                # Create a test industry
                create_industry_data = {
                    "name": "Test Industry",
                    "description": "Test industry for filtering"
                }
                response = await client.post(f"{BASE_URL}/api/industries", json=create_industry_data, headers=headers)
                if response.status_code == 201:
                    industry_data = response.json()
                    industry_id = industry_data["data"]["id"]
                    industry_name = industry_data["data"]["name"]
                    print(f"✅ Created test industry: {industry_name} (ID: {industry_id})")
                else:
                    print(f"❌ Failed to create industry: {response.status_code}")
                    return
        else:
            print(f"❌ Failed to get industries: {response.status_code}")
            return
        
        # 3. Test filtering by industry ID
        print("3. Testing filter by industry ID...")
        response = await client.get(
            f"{BASE_URL}/api/franchisors/",
            params={"industry": industry_id, "limit": 5},
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            items = data.get("data", {}).get("items", [])
            print(f"✅ Filter by industry ID works: {len(items)} franchisors found")
            
            # Verify all returned franchisors have the correct industry
            for item in items:
                if item.get("industry_id") == industry_id:
                    print(f"   ✅ Franchisor '{item['name']}' has correct industry ID")
                else:
                    print(f"   ❌ Franchisor '{item['name']}' has wrong industry ID: {item.get('industry_id')}")
        else:
            print(f"❌ Filter by industry ID failed: {response.status_code}")
        
        # 4. Test filtering by industry name
        print("4. Testing filter by industry name...")
        response = await client.get(
            f"{BASE_URL}/api/franchisors/",
            params={"industry": industry_name, "limit": 5},
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            items = data.get("data", {}).get("items", [])
            print(f"✅ Filter by industry name works: {len(items)} franchisors found")
            
            # Verify all returned franchisors have the correct industry
            for item in items:
                industry_details = item.get("industry_details", {})
                if industry_details and industry_details.get("name") == industry_name:
                    print(f"   ✅ Franchisor '{item['name']}' has correct industry name")
                else:
                    print(f"   ❌ Franchisor '{item['name']}' has wrong industry: {industry_details.get('name') if industry_details else 'None'}")
        else:
            print(f"❌ Filter by industry name failed: {response.status_code}")
        
        # 5. Test with invalid UUID
        print("5. Testing with invalid UUID...")
        response = await client.get(
            f"{BASE_URL}/api/franchisors/",
            params={"industry": "invalid-uuid", "limit": 5},
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            items = data.get("data", {}).get("items", [])
            print(f"✅ Invalid UUID handled gracefully: {len(items)} franchisors found (treated as name search)")
        else:
            print(f"❌ Invalid UUID handling failed: {response.status_code}")
        
        # 6. Test with partial name match
        print("6. Testing partial name match...")
        partial_name = industry_name[:3] if len(industry_name) > 3 else industry_name
        response = await client.get(
            f"{BASE_URL}/api/franchisors/",
            params={"industry": partial_name, "limit": 5},
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            items = data.get("data", {}).get("items", [])
            print(f"✅ Partial name match works: {len(items)} franchisors found for '{partial_name}'")
        else:
            print(f"❌ Partial name match failed: {response.status_code}")
        
        print("\n✅ Industry filter tests completed!")

if __name__ == "__main__":
    asyncio.run(test_industry_filter())
