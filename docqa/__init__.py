"""
DocQA - Universal Document Question Answering System with pgvector

A robust and modular CLI + Python system for document ingestion and question answering
using pgvector for vector storage and OpenAI for embeddings and completions.

Features:
- Smart ingestion pipeline for any document type (PDF, DOC, DOCX, TXT, etc.)
- pgvector-based vector storage with dynamic content processing
- Adaptive processing for different document types and content
- Central ask_question() API for external integration
- OCR, text extraction, and image analysis capabilities
- Comprehensive testing and logging
"""

__version__ = "1.0.0"
__author__ = "GrowthHive Team"

from .serve import ask_question, ask_question_production, ask_brochure_question

# Production-grade RAG system
from .production_integration import production_rag, ProductionRAGSystem

__all__ = [
    "ask_question",
    "ask_question_production",
    "ask_brochure_question",
    "production_rag",
    "ProductionRAGSystem"
]
