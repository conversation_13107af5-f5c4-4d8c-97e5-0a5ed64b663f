# Background Brochure Processing Implementation

This document describes the implementation of background processing for franchisor brochure uploads using RabbitMQ, Redis, and Celery.

## 🎯 Overview

The `/api/franchisors/{franchisor_id}/upload-brochure` endpoint has been enhanced to:

1. **Upload file to S3** - Immediate file storage
2. **Update franchisor record** - Store brochure URL in database
3. **Queue background processing** - Use Celery/RabbitMQ for async processing
4. **Return immediately** - Provide task ID for status tracking
5. **Process in background** - Document ingestion, embedding generation, etc.

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────┐    ┌─────────────────┐
│   FastAPI       │    │   RabbitMQ   │    │   Celery        │
│   Endpoint      │───▶│   Queue      │───▶│   Worker        │
└─────────────────┘    └──────────────┘    └─────────────────┘
         │                                           │
         ▼                                           ▼
┌─────────────────┐                        ┌─────────────────┐
│   S3 Storage    │                        │   DocQA +       │
│   (Immediate)   │                        │   Agent System  │
└─────────────────┘                        └─────────────────┘
         │                                           │
         ▼                                           ▼
┌─────────────────┐    ┌──────────────┐    ┌─────────────────┐
│   PostgreSQL    │◀───│    Redis     │◀───│   Processing    │
│   (Franchisor)  │    │   (Status)   │    │   Results       │
└─────────────────┘    └──────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### 1. Start Background Services

**Option A: Using Docker (Recommended)**
```bash
# Start RabbitMQ, Redis, and Celery workers
./scripts/start_background_services.sh
```

**Option B: Local Development**
```bash
# Start RabbitMQ and Redis with Docker
docker-compose -f docker-compose.rabbitmq.yml up -d rabbitmq redis

# Start Celery worker locally
./scripts/start_celery_local.sh
```

### 2. Start FastAPI Server
```bash
# Activate virtual environment
source venv311/bin/activate

# Start server
python start_server.py
```

### 3. Test the Implementation
```bash
# Run the test script
python test_background_brochure_upload.py
```

## 📡 API Usage

### Upload Brochure with Background Processing

**Endpoint:** `POST /api/franchisors/{franchisor_id}/upload-brochure`

**Request:**
```bash
curl -X POST "http://localhost:8000/api/franchisors/frc_123456789/upload-brochure" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@brochure.pdf"
```

**Response:**
```json
{
  "success": true,
  "status": "success",
  "message": {
    "title": "Brochure Uploaded",
    "description": "PDF brochure uploaded successfully and queued for background processing. Task ID: task_abc123"
  },
  "data": {
    "id": "frc_123456789",
    "name": "Coffee Club Melbourne",
    "brochure_url": "https://s3.amazonaws.com/bucket/brochures/20240101_123456_abc123.pdf",
    "processing_task_id": "task_abc123",
    "processing_status": "queued",
    "processing_message": "Document queued for background processing"
  }
}
```

### Monitor Task Progress

**Endpoint:** `GET /api/background-tasks/status/{task_id}`

**Request:**
```bash
curl "http://localhost:8000/api/background-tasks/status/task_abc123"
```

**Response:**
```json
{
  "task_id": "task_abc123",
  "status": "SUCCESS",
  "progress": 100,
  "result": {
    "success": true,
    "franchisor_id": "frc_123456789",
    "docqa_success": true,
    "agent_success": true,
    "processing_details": {
      "docqa_chunks_created": 25,
      "docqa_processing_time": 45.2,
      "agent_error": null
    }
  }
}
```

## 🔧 Configuration

### Environment Variables

```bash
# RabbitMQ Configuration
RABBITMQ_URL=amqp://growthhive:growthhive123@localhost:5672//
CELERY_BROKER_URL=amqp://growthhive:growthhive123@localhost:5672//

# Redis Configuration
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Task Configuration
DOCUMENT_PROCESSING_QUEUE=document_processing
DOCUMENT_PROCESSING_RETRY_DELAY=60
DOCUMENT_PROCESSING_MAX_RETRIES=3
```

## 🔍 Monitoring

### Service URLs
- **RabbitMQ Management**: http://localhost:15672 (growthhive/growthhive123)
- **Celery Flower**: http://localhost:5555
- **Redis**: localhost:6379

### Task Status Values
- `PENDING` - Task queued but not started
- `PROCESSING` - Task currently running
- `SUCCESS` - Task completed successfully
- `FAILURE` - Task failed permanently
- `RETRY` - Task failed but will retry

## 🛠️ Implementation Details

### Enhanced Endpoint Features

1. **Immediate Response**: File upload and database update happen synchronously
2. **Background Queuing**: Document processing is queued using Celery
3. **Dual Processing**: Both DocQA service and Agent system process the document
4. **Error Handling**: Graceful degradation if queuing fails
5. **Status Tracking**: Task ID returned for progress monitoring

### Processing Pipeline

1. **DocQA Processing**: Traditional document ingestion and chunking
2. **Agent Processing**: Enhanced processing using the agent orchestrator
3. **Result Aggregation**: Combines results from both systems
4. **Status Updates**: Real-time progress tracking via Redis

### Error Handling

- **Upload Failures**: Return appropriate error responses
- **Queue Failures**: File still uploaded, warning returned
- **Processing Failures**: Retry mechanism with exponential backoff
- **Partial Failures**: Success if either DocQA or Agent processing succeeds

## 🧪 Testing

### Manual Testing
```bash
# 1. Upload a brochure
curl -X POST "http://localhost:8000/api/franchisors/frc_123456789/upload-brochure" \
  -F "file=@test_brochure.pdf"

# 2. Monitor progress
curl "http://localhost:8000/api/background-tasks/status/{task_id}"

# 3. Verify franchisor update
curl "http://localhost:8000/api/franchisors/frc_123456789"
```

### Automated Testing
```bash
python test_background_brochure_upload.py
```

## 📊 Performance Benefits

- **Faster Response Times**: Immediate API response (< 2 seconds)
- **Better User Experience**: No waiting for document processing
- **Scalability**: Multiple workers can process documents in parallel
- **Reliability**: Retry mechanism for failed processing
- **Monitoring**: Real-time status tracking and error reporting

## 🔒 Security Considerations

- File validation before upload
- Authentication required for all endpoints
- S3 access controls
- Task queue isolation
- Error message sanitization

## 🚨 Troubleshooting

### Common Issues

1. **RabbitMQ Connection Failed**
   - Check if RabbitMQ is running
   - Verify credentials in environment variables

2. **Redis Connection Failed**
   - Ensure Redis is running on correct port
   - Check Redis configuration

3. **Task Not Processing**
   - Verify Celery worker is running
   - Check worker logs for errors

4. **Processing Failures**
   - Check DocQA service availability
   - Verify Agent system configuration
   - Review task logs in Celery Flower

### Logs and Debugging

```bash
# View Celery worker logs
docker-compose -f docker-compose.rabbitmq.yml logs -f celery-worker

# Check task status in Flower
# Visit http://localhost:5555

# View application logs
tail -f logs/app.log
```
